title: Overwriting the File with Dev Zero or Null
id: 37222991-11e9-4b6d-8bdf-60fbe48f753e
status: stable
description: Detects overwriting (effectively wiping/deleting) of a file.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1485/T1485.md
author: <PERSON>, oscd.community
date: 2019-10-23
tags:
    - attack.impact
    - attack.t1485
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'EXECVE'
        a0|contains: 'dd'
        a1|contains:
            - 'if=/dev/null'
            - 'if=/dev/zero'
    condition: selection
falsepositives:
    - Appending null bytes to files.
    - Legitimate overwrite of files.
level: low
