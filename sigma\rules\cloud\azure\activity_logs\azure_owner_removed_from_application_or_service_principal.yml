title: Azure Owner Removed From Application or Service Principal
id: 636e30d5-3736-42ea-96b1-e6e2f8429fd6
status: test
description: Identifies when a owner is was removed from a application or service principal in Azure.
references:
    - https://learn.microsoft.com/en-us/entra/identity/monitoring-health/reference-audit-activities#application-proxy
author: <PERSON> @austinsonger
date: 2021-09-03
modified: 2022-10-09
tags:
    - attack.defense-evasion
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        properties.message:
            - Remove owner from service principal
            - Remove owner from application
    condition: selection
falsepositives:
    - Owner being removed may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Owner removed from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
