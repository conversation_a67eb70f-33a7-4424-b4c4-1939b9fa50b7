title: Container With A hostPath Mount Created
id: 402b955c-8fe0-4a8c-b635-622b4ac5f902
status: test
description: |
    Detects creation of a container with a hostPath mount.
    A hostPath volume mounts a directory or a file from the node to the container.
    Attackers who have permissions to create a new pod in the cluster may create one with a writable hostPath volume and chroot to escape to the underlying node.
references:
    - https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/Writable%20hostPath%20mount/
    - https://blog.appsecco.com/kubernetes-namespace-breakout-using-insecure-host-path-volume-part-1-b382f2a6e216
author: <PERSON> (@laripping)
date: 2024-03-26
tags:
    - attack.t1611
    - attack.privilege-escalation
logsource:
    category: application
    product: kubernetes
    service: audit
detection:
    selection:
        verb: 'create'
        objectRef.resource: 'pods'
        hostPath: '*' # Note: Add the "exists" when it's implemented in SigmaHQ/Aurora
    condition: selection
falsepositives:
    - The DaemonSet controller creates pods with hostPath volumes within the kube-system namespace.
level: low
