title: Remote Schedule Task Recon via AtScv
id: f177f2bc-5f3e-4453-b599-57eefce9a59c
status: test
description: Detects remote RPC calls to read information about scheduled tasks via AtScv
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-tsch/d1058a28-7e02-4948-8b8d-4a347fa64931
    - https://github.com/zeronetworks/rpcfirewall
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-TSCH.md
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, De<PERSON>
date: 2022-01-01
tags:
    - attack.discovery
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:1ff70682-0a51-30e8-076d-740be8cee98b"'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 1ff70682-0a51-30e8-076d-740be8cee98b
    filter:
        OpNum:
            - 0
            - 1
    condition: selection and not filter
falsepositives:
    - Unknown
level: high
