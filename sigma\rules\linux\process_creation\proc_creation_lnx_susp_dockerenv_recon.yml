title: Docker Container Discovery Via Dockerenv Listing
id: ************************************
status: test
description: Detects listing or file reading of ".dockerenv" which can be a sing of potential container discovery
references:
    - https://blog.skyplabs.net/posts/container-detection/
    - https://stackoverflow.com/questions/20010199/how-to-determine-if-a-process-runs-inside-lxc-docker
tags:
    - attack.discovery
    - attack.t1082
author: <PERSON>
date: 2023-08-23
logsource:
    category: process_creation
    product: linux
detection:
    selection:
        Image|endswith:
            # Note: add additional tools and utilities to increase coverage
            - '/cat'
            - '/dir'
            - '/find'
            - '/ls'
            - '/stat'
            - '/test'
            - 'grep'
        CommandLine|endswith: '.dockerenv'
    condition: selection
falsepositives:
    - Legitimate system administrator usage of these commands
    - Some container tools or deployments may use these techniques natively to determine how they proceed with execution, and will need to be filtered
level: low
