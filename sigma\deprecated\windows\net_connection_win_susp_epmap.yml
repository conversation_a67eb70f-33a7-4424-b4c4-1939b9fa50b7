title: Suspicious Epmap Connection
id: 628d7a0b-7b84-4466-8552-e6138bc03b43
status: deprecated
description: Detects suspicious "epmap" connection to a remote computer via remote procedure call (RPC)
references:
    - https://github.com/RiccardoAncarani/TaskShell/
author: frack113, <PERSON> (fps)
date: 2022/07/14
modified: 2024/03/01
tags:
    - attack.lateral_movement
logsource:
    category: network_connection
    product: windows
detection:
    selection:
        Protocol: tcp
        Initiated: 'true'
        DestinationPort: 135
        # DestinationPortName: epmap
    filter_image:
        Image|startswith:
            - C:\Windows\
            - C:\ProgramData\Amazon\SSM\Update\amazon-ssm-agent-updater
    filter_image_null1:
        Image: null
    filter_image_null2:
        Image: ''
    filter_image_unknown:
        Image: '<unknown process>'
    condition: selection and not 1 of filter_*
falsepositives:
    - Unknown
level: high
