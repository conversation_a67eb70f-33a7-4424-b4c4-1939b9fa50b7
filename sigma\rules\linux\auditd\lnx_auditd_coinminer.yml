title: Possible Coin Miner CPU Priority Param
id: 071d5e5a-9cef-47ec-bc4e-a42e34d8d0ed
status: test
description: Detects command line parameter very often used with coin miners
references:
    - https://xmrig.com/docs/miner/command-line-options
author: <PERSON><PERSON><PERSON> (Nextron Systems)
date: 2021-10-09
modified: 2022-12-25
tags:
    - attack.privilege-escalation
    - attack.t1068
logsource:
    product: linux
    service: auditd
detection:
    cmd1:
        a1|startswith: '--cpu-priority'
    cmd2:
        a2|startswith: '--cpu-priority'
    cmd3:
        a3|startswith: '--cpu-priority'
    cmd4:
        a4|startswith: '--cpu-priority'
    cmd5:
        a5|startswith: '--cpu-priority'
    cmd6:
        a6|startswith: '--cpu-priority'
    cmd7:
        a7|startswith: '--cpu-priority'
    condition: 1 of cmd*
falsepositives:
    - Other tools that use a --cpu-priority flag
level: critical
