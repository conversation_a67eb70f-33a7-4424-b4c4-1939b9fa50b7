title: Login to Disabled Account
id: 908655e0-25cf-4ae1-b775-1c8ce9cf43d8
status: test
description: Detect failed attempts to sign in to disabled accounts.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts
author: AlertIQ
date: 2021-10-10
modified: 2022-12-25
tags:
    - attack.initial-access
    - attack.t1078.004
logsource:
    product: azure
    service: signinlogs
detection:
    selection:
        ResultType: 50057
        ResultDescription: 'User account is disabled. The account has been disabled by an administrator.'
    condition: selection
falsepositives:
    - Unknown
level: medium
