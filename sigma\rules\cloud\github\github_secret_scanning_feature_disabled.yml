title: Github Secret Scanning Feature Disabled
id: 3883d9a0-fd0f-440f-afbb-445a2a799bb8
status: test
description: Detects if the secret scanning feature is disabled for an enterprise or repository.
references:
    - https://docs.github.com/en/enterprise-cloud@latest/code-security/secret-scanning/about-secret-scanning
author: <PERSON> (@faisalusuf)
date: 2024-03-07
modified: 2024-07-19
tags:
    - attack.defense-evasion
    - attack.t1562.001
logsource:
    product: github
    service: audit
    definition: 'Requirements: The audit log streaming feature must be enabled to be able to receive such logs. You can enable following the documentation here: https://docs.github.com/en/enterprise-cloud@latest/admin/monitoring-activity-in-your-enterprise/reviewing-audit-logs-for-your-enterprise/streaming-the-audit-log-for-your-enterprise#setting-up-audit-log-streaming'
detection:
    selection:
        action:
            - 'business_secret_scanning.disable'
            - 'business_secret_scanning.disabled_for_new_repos'
            - 'repository_secret_scanning.disable'
            - 'secret_scanning_new_repos.disable'
            - 'secret_scanning.disable'
    condition: selection
falsepositives:
    - Allowed administrative activities.
level: high
