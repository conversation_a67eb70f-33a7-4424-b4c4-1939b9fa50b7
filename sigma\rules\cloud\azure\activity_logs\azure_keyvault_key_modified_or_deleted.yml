title: Azure Keyvault Key Modified or Deleted
id: 80eeab92-0979-4152-942d-96749e11df40
status: test
description: Identifies when a Keyvault Key is modified or deleted in Azure.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON> @austinsonger
date: 2021-08-16
modified: 2022-08-23
tags:
    - attack.impact
    - attack.credential-access
    - attack.t1552
    - attack.t1552.001
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/UPDATE/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/CREATE
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/CREATE/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/IMPORT/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/RECOVER/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/RESTORE/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/DELETE
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/BACKUP/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/KEYS/PURGE/ACTION
    condition: selection
falsepositives:
    - Key being modified or deleted may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Key modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
