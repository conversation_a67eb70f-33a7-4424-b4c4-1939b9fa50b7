title: Azure Application Gateway Modified or Deleted
id: ad87d14e-7599-4633-ba81-aeb60cfe8cd6
status: test
description: Identifies when a application gateway is modified or deleted.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON>
date: 2021-08-16
modified: 2022-08-23
tags:
    - attack.impact
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.NETWORK/APPLICATIONGATEWAYS/WRITE
            - MICROSOFT.NETWORK/APPLICATIONGATEWAYS/DELETE
    condition: selection
falsepositives:
    - Application gateway being modified or deleted may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Application gateway modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
