title: Kubernetes Secrets Modified or Deleted
id: 58d31a75-a4f8-4c40-985b-373d58162ca2
related:
    - id: 2f0bae2d-bf20-4465-be86-1311addebaa3
      type: similar
status: test
description: |
    Detects when Kubernetes Secrets are Modified or Deleted.
references:
    - https://kubernetes.io/docs/reference/config-api/apiserver-audit.v1/
    - https://commandk.dev/blog/guide-to-audit-k8s-secrets-for-compliance/
author: kelnage
date: 2024-07-11
tags:
    - attack.credential-access
logsource:
    product: kubernetes
    service: audit
detection:
    selection:
        objectRef.resource: 'secrets'
        verb:
            - 'create'
            - 'delete'
            - 'patch'
            - 'replace'
            - 'update'
    condition: selection
falsepositives:
    - Secrets being modified or deleted may be performed by a system administrator.
    - Automated processes may need to take these actions and may need to be filtered.
level: medium
