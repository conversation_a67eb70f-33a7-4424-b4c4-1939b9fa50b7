title: Google Workspace MFA Disabled
id: 780601d1-6376-4f2a-884e-b8d45599f78c
status: test
description: Detects when multi-factor authentication (MFA) is disabled.
references:
    - https://cloud.google.com/logging/docs/audit/gsuite-audit-logging#3
    - https://developers.google.com/admin-sdk/reports/v1/appendix/activity/admin-security-settings#ENFORCE_STRONG_AUTHENTICATION
    - https://developers.google.com/admin-sdk/reports/v1/appendix/activity/admin-security-settings?hl=en#ALLOW_STRONG_AUTHENTICATION
author: <PERSON>
date: 2021-08-26
modified: 2023-10-11
tags:
    - attack.impact
logsource:
    product: gcp
    service: google_workspace.admin
detection:
    selection_base:
        eventService: admin.googleapis.com
        eventName:
            - ENFORCE_STRONG_AUTHENTICATION
            - ALLOW_STRONG_AUTHENTICATION
    selection_eventValue:
        new_value: 'false'
    condition: all of selection*
falsepositives:
    - <PERSON><PERSON> may be disabled and performed by a system administrator.
level: medium
