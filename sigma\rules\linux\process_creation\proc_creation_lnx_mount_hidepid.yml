title: Mount Execution With Hidepid Parameter
id: ec52985a-d024-41e3-8ff6-14169039a0b3
status: test
description: Detects execution of the "mount" command with "hidepid" parameter to make invisible processes to other users from the system
references:
    - https://blogs.blackberry.com/
    - https://www.cyberciti.biz/faq/linux-hide-processes-from-other-users/
    - https://twitter.com/Joseliyo_Jstnk/status/1620131033474822144
author: <PERSON><PERSON><PERSON>, @Joseliyo_Jstnk
date: 2023-01-12
tags:
    - attack.credential-access
    - attack.defense-evasion
    - attack.t1564
logsource:
    product: linux
    category: process_creation
detection:
    selection:
        Image|endswith: '/mount'
        CommandLine|contains|all:
            - 'hidepid=2'
            - ' -o '
    condition: selection
falsepositives:
    - Unknown
level: medium
