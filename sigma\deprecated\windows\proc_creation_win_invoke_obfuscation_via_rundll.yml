title: Invoke-Obfuscation RUNDLL LAUNCHER
id: 056a7ee1-4853-4e67-86a0-3fd9ceed7555
status: deprecated
description: Detects Obfuscated Powershell via RUNDLL LAUNCHER
references:
    - https://github.com/SigmaHQ/sigma/issues/1009 #(Task 23)
author: <PERSON><PERSON>, oscd.community
date: 2020/10/18
modified: 2023/02/21
tags:
    - attack.defense_evasion
    - attack.t1027
    - attack.execution
    - attack.t1059.001
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        CommandLine|contains|all:
            - 'rundll32.exe'
            - 'shell32.dll'
            - 'shellexec_rundll'
            - 'powershell'
    condition: selection
falsepositives:
    - Unknown
level: medium
