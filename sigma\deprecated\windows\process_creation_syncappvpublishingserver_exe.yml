title: SyncAppvPublishingServer Execution to Bypass Powershell Restriction
id: fde7929d-8beb-4a4c-b922-be9974671667
description: Detects SyncAppvPublishingServer process execution which usually utilized by adversaries to bypass PowerShell execution restrictions.
references:
    - https://lolbas-project.github.io/lolbas/Binaries/Syncappvpublishingserver/
author: '<PERSON><PERSON>, @sblmsrsn, OSCD Community'
date: 2020/10/05
modified: 2022/04/11
tags:
    - attack.defense_evasion
    - attack.t1218
logsource:
    product: windows
    category: process_creation
detection:
    selection:
        Image|endswith: '\SyncAppvPublishingServer.exe'
    condition: selection
falsepositives:
    - App-V clients
level: medium
status: deprecated