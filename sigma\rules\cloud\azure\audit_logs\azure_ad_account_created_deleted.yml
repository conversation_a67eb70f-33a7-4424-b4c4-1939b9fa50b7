title: Account Created And Deleted Within A Close Time Frame
id: 6f583da0-3a90-4566-a4ed-83c09fe18bbf
status: test
description: Detects when an account was created and deleted in a short period of time.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-user-accounts#short-lived-accounts
author: <PERSON> '@markmorow', <PERSON><PERSON><PERSON><PERSON><PERSON>, '@dudders1', <PERSON>
date: 2022-08-11
modified: 2022-08-18
tags:
    - attack.defense-evasion
    - attack.t1078
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message:
            - Add user
            - Delete user
        Status: Success
    condition: selection
falsepositives:
    - Legit administrative action
level: high
