title: Credential Dumping Tools Service Execution
id: df5ff0a5-f83f-4a5b-bba1-3e6a3f6f6ea2
related:
    - id: 4976aa50-8f41-45c6-8b15-ab3fc10e79ed
      type: derived
status: deprecated
description: Detects well-known credential dumping tools execution via service execution events
references:
    - https://www.slideshare.net/heirhabarov/hunting-for-credentials-dumping-in-windows-environment
author: <PERSON><PERSON><PERSON> (Nextron Systems), <PERSON><PERSON><PERSON>, <PERSON><PERSON>, oscd.community
date: 2017/03/05
modified: 2023/12/11
tags:
    - attack.credential_access
    - attack.execution
    - attack.t1003.001
    - attack.t1003.002
    - attack.t1003.004
    - attack.t1003.005
    - attack.t1003.006
    - attack.t1569.002
    - attack.s0005
logsource:
    product: windows
    category: driver_load
detection:
    selection:
        ImageLoaded|contains:
            - 'cachedump'
            - 'dumpsvc'
            - 'fgexec'
            - 'gsecdump'
            - 'mimidrv'
            - 'pwdump'
            - 'servpw'
    condition: selection
falsepositives:
    - Legitimate Administrator using credential dumping tool for password recovery
level: critical
