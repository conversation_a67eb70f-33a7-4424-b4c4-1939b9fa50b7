title: PsExec Tool Execution
id: fa91cc36-24c9-41ce-b3c8-3bbc3f2f67ba
related:
    - id: 42c575ea-e41e-41f1-b248-8093c3e82a28
      type: derived
status: deprecated
description: Detects PsExec service execution via default service image name
references:
    - https://www.jpcert.or.jp/english/pub/sr/ir_research.html
    - https://jpcertcc.github.io/ToolAnalysisResultSheet
author: <PERSON>
date: 2017/06/12
modified: 2023/02/28
tags:
    - attack.execution
    - attack.t1569.002
    - attack.s0029
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        Image|endswith: '\PSEXESVC.exe'
        User|contains: # covers many language settings
            - 'AUTHORI'
            - 'AUTORI'
    condition: selection
fields:
    - EventID
    - CommandLine
    - ParentCommandLine
    - ServiceName
    - ServiceFileName
    - TargetFilename
    - PipeName
falsepositives:
    - Unknown
level: low
