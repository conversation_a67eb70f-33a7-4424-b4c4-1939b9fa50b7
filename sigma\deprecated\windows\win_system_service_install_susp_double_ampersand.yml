title: New Service Uses Double Ampersand in Path
id: ca83e9f3-657a-45d0-88d6-c1ac280caf53
status: deprecated
description: Detects a service installation that uses a suspicious double ampersand used in the image path value
references:
    - Internal Research
author: <PERSON><PERSON><PERSON> (Nextron Systems)
date: 2022/07/05
modified: 2023/11/15
tags:
    - attack.defense_evasion
    - attack.t1027
logsource:
    product: windows
    service: system
detection:
    selection:
        Provider_Name: 'Service Control Manager'
        EventID: 7045
        ImagePath|contains: '&&'
    condition: selection
falsepositives:
    - Unknown
level: high
