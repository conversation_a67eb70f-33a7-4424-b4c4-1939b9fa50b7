title: CA Policy Updated by Non Approved Actor
id: 50a3c7aa-ec29-44a4-92c1-fce229eef6fc
status: test
description: Monitor and alert on conditional access changes. Is Initiated by (actor) approved to make changes? Review Modified Properties and compare "old" vs "new" value.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-infrastructure#conditional-access
author: <PERSON><PERSON>, '@corissalea'
date: 2022-07-19
modified: 2024-05-28
tags:
    - attack.defense-evasion
    - attack.persistence
    - attack.t1548
    - attack.t1556
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message: Update conditional access policy
    condition: selection
falsepositives:
    - Misconfigured role permissions
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
level: medium
