title: Azure Active Directory Hybrid Health AD FS New Server
id: 288a39fc-4914-4831-9ada-270e9dc12cb4
status: test
description: |
    This detection uses azureactivity logs (Administrative category) to identify the creation or update of a server instance in an Azure AD Hybrid health AD FS service.
    A threat actor can create a new AD Health ADFS service and create a fake server instance to spoof AD FS signing logs. There is no need to compromise an on-prem AD FS server.
    This can be done programmatically via HTTP requests to Azure.
references:
    - https://o365blog.com/post/hybridhealthagent/
author: <PERSON> (Cyb3rWard0g), <PERSON><PERSON> (Open Threat Research), MSTIC
date: 2021-08-26
modified: 2023-10-11
tags:
    - attack.defense-evasion
    - attack.t1578
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        CategoryValue: 'Administrative'
        ResourceProviderValue: 'Microsoft.ADHybridHealthService'
        ResourceId|contains: 'AdFederationService'
        OperationNameValue: 'Microsoft.ADHybridHealthService/services/servicemembers/action'
    condition: selection
falsepositives:
    - Legitimate AD FS servers added to an AAD Health AD FS service instance
level: medium
