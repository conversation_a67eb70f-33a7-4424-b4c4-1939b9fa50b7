title: OpenCanary - MSSQL Login Attempt Via SQLAuth
id: 3ec9a16d-0b4f-4967-9542-ebf38ceac7dd
status: test
description: |
    Detects instances where an MSSQL service on an OpenCanary node has had a login attempt using SQLAuth.
references:
    - https://opencanary.readthedocs.io/en/latest/starting/configuration.html#services-configuration
    - https://github.com/thinkst/opencanary/blob/a0896adfcaf0328cfd5829fe10d2878c7445138e/opencanary/logger.py#L52
author: Security Onion Solutions
date: 2024-03-08
tags:
    - attack.credential-access
    - attack.collection
    - attack.t1003
    - attack.t1213
logsource:
    category: application
    product: opencanary
detection:
    selection:
        logtype: 9001
    condition: selection
falsepositives:
    - Unlikely
level: high
