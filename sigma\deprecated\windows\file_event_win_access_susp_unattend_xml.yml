title: Suspicious Unattend.xml File Access
id: 1a3d42dd-3763-46b9-8025-b5f17f340dfb
status: deprecated
description: |
    Attempts to access unattend.xml, where credentials are commonly stored, within the Panther directory where installation logs are stored.
    If these files exist, their contents will be displayed. They are used to store credentials/answers during the unattended windows install process
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1552.001/T1552.001.md
author: frack113
date: 2021/12/19
modified: 2024/07/22
tags:
    - attack.credential_access
    - attack.t1552.001
logsource:
    product: windows
    category: file_event
detection:
    selection:
        TargetFilename|endswith: '\unattend.xml'
    condition: selection
falsepositives:
    - Unknown
level: medium
