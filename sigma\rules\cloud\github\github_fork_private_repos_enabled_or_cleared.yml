title: Github Fork Private Repositories Setting Enabled/Cleared
id: 69b3bd1e-b38a-462f-9a23-fbdbf63d2294
status: test
description: |
    Detects when the policy allowing forks of private and internal repositories is changed (enabled or cleared).
references:
    - https://docs.github.com/en/enterprise-cloud@latest/admin/monitoring-activity-in-your-enterprise/reviewing-audit-logs-for-your-enterprise/audit-log-events-for-your-enterprise#private_repository_forking
author: <PERSON><PERSON> (@romain-gail<PERSON>)
date: 2024-07-29
tags:
    - attack.persistence
    - attack.exfiltration
    - attack.t1020
    - attack.t1537
logsource:
    product: github
    service: audit
    definition: 'Requirements: The audit log streaming feature must be enabled to be able to receive such logs. You can enable following the documentation here: https://docs.github.com/en/enterprise-cloud@latest/admin/monitoring-activity-in-your-enterprise/reviewing-audit-logs-for-your-enterprise/streaming-the-audit-log-for-your-enterprise#setting-up-audit-log-streaming'
detection:
    selection:
        action:
            - 'private_repository_forking.clear' # An enterprise owner cleared the policy setting for allowing forks of private and internal repositories, for a repository, organization or enterprise.
            - 'private_repository_forking.enable' # An enterprise owner enabled the policy setting for allowing forks of private and internal repositories, for a repository, organization or enterprise. Private and internal repositories are always allowed to be forked.
    condition: selection
falsepositives:
    - Allowed administrative activities.
level: medium
