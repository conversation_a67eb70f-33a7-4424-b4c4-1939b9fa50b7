title: OpenCanary - Telnet Login Attempt
id: 512cff7a-683a-43ad-afe0-dd398e872f36
status: test
description: Detects instances where a Telnet service on an OpenCanary node has had a login attempt.
references:
    - https://opencanary.readthedocs.io/en/latest/starting/configuration.html#services-configuration
    - https://github.com/thinkst/opencanary/blob/a0896adfcaf0328cfd5829fe10d2878c7445138e/opencanary/logger.py#L52
author: Security Onion Solutions
date: 2024-03-08
tags:
    - attack.initial-access
    - attack.command-and-control
    - attack.t1133
    - attack.t1078
logsource:
    category: application
    product: opencanary
detection:
    selection:
        logtype: 6001
    condition: selection
falsepositives:
    - Unlikely
level: high
