title: Activity Related to NTDS.dit Domain Hash Retrieval
id: b932b60f-fdda-4d53-8eda-a170c1d97bbd
status: deprecated
description: Detects suspicious commands that could be related to activity that uses volume shadow copy to steal and retrieve hashes from the NTDS.dit file remotely
author: <PERSON><PERSON><PERSON> (Nextron Systems), <PERSON>
date: 2019/01/16
modified: 2022/04/11
references:
    - https://www.swordshield.com/2015/07/getting-hashes-from-ntds-dit-file/
    - https://room362.com/post/2013/2013-06-10-volume-shadow-copy-ntdsdit-domain-hashes-remotely-part-1/
    - https://www.trustwave.com/Resources/SpiderLabs-Blog/Tutorial-for-NTDS-goodness-(VSSADMIN,-WMIS,-NTDS-dit,-SYSTEM)/
    - https://securingtomorrow.mcafee.com/mcafee-labs/new-teslacrypt-ransomware-arrives-via-spam/
    - https://dfironthemountain.wordpress.com/2018/12/06/locked-file-access-using-esentutl-exe/
tags:
    - attack.credential_access
    - attack.t1003
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        CommandLine:
            - 'vssadmin.exe Delete Shadows'
            - 'vssadmin create shadow /for=C:'
            - 'copy \\?\GLOBALROOT\Device\\*\windows\ntds\ntds.dit'
            - 'copy \\?\GLOBALROOT\Device\\*\config\SAM'
            - 'vssadmin delete shadows /for=C:'
            - 'reg SAVE HKLM\SYSTEM '
            - 'esentutl.exe /y /vss *\ntds.dit*'
            - 'esentutl.exe /y /vss *\SAM'
            - 'esentutl.exe /y /vss *\SYSTEM'
    condition: selection
fields:
    - CommandLine
    - ParentCommandLine
falsepositives:
    - Administrative activity
level: high
