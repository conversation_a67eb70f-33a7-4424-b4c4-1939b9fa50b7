title: Process Execution Error In JVM Based Application
id: d65f37da-a26a-48f8-8159-3dde96680ad2
status: test
description: Detects process execution related exceptions in JVM based apps, often relates to RCE
references:
    - https://www.wix.engineering/post/threat-and-vulnerability-hunting-with-application-server-error-logs
author: <PERSON><PERSON>
date: 2023-02-11
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: jvm
    definition: 'Requirements: application error logs must be collected (with LOG_LEVEL=ERROR and above)'
detection:
    keywords:
        - 'Cannot run program'
        - 'java.lang.ProcessImpl'
        - 'java.lang.ProcessBuilder'
    condition: keywords
falsepositives:
    - Application bugs
level: high
