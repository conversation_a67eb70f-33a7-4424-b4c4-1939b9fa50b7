title: CA Policy Removed by Non Approved Actor
id: 26e7c5e2-6545-481e-b7e6-050143459635
status: test
description: Monitor and alert on conditional access changes where non approved actor removed CA Policy.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-infrastructure#conditional-access
author: <PERSON><PERSON>, '@corissalea'
date: 2022-07-19
tags:
    - attack.defense-evasion
    - attack.persistence
    - attack.t1548
    - attack.t1556
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message: Delete conditional access policy
    condition: selection
falsepositives:
    - Misconfigured role permissions
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
level: medium
