title: Suspicious Certutil Command Usage
id: e011a729-98a6-4139-b5c4-bf6f6dd8239a
status: deprecated
description: Detects a suspicious Microsoft certutil execution with sub commands like 'decode' sub command, which is sometimes used to decode malicious code
references:
    - https://twitter.com/JohnLaTwC/status/835149808817991680
    - https://blogs.technet.microsoft.com/pki/2006/11/30/basic-crl-checking-with-certutil/
    - https://www.trustedsec.com/2017/07/new-tool-release-nps_payload/
    - https://twitter.com/egre55/status/1087685529016193025
    - https://lolbas-project.github.io/lolbas/Binaries/Certutil/
author: <PERSON><PERSON><PERSON> (Nextron Systems), juju4, keepwatch
date: 2019/01/16
modified: 2023/02/15
tags:
    - attack.defense_evasion
    - attack.t1140
    - attack.command_and_control
    - attack.t1105
    - attack.s0160
    - attack.g0007
    - attack.g0010
    - attack.g0045
    - attack.g0049
    - attack.g0075
    - attack.g0096
logsource:
    category: process_creation
    product: windows
detection:
    selection_img:
        - Image|endswith: '\certutil.exe'
        - OriginalFileName: 'CertUtil.exe'
    selection_cli:
        CommandLine|contains:
            - ' -decode '
            - ' -decodehex '
            - ' -urlcache '
            - ' -verifyctl '
            - ' -encode '
            - ' -exportPFX '
            - ' /decode '
            - ' /decodehex '
            - ' /urlcache '
            - ' /verifyctl '
            - ' /encode '
            - ' /exportPFX '
    condition: all of selection_*
fields:
    - CommandLine
    - ParentCommandLine
falsepositives:
    - False positives depend on scripts and administrative tools used in the monitored environment
level: high
