title: Azure Domain Federation Settings Modified
id: 352a54e1-74ba-4929-9d47-8193d67aba1e
status: test
description: Identifies when an user or application modified the federation settings on the domain.
references:
    - https://learn.microsoft.com/en-us/azure/active-directory/hybrid/how-to-connect-monitor-federation-changes
author: <PERSON>
date: 2021-09-06
modified: 2022-06-08
tags:
    - attack.initial-access
    - attack.t1078
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        ActivityDisplayName: Set federation settings on domain
    condition: selection
falsepositives:
    - Federation Settings being modified or deleted may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Federation Settings modified from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.

level: medium
