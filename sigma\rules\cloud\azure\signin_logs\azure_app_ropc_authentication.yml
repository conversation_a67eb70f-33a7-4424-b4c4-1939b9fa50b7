title: Applications That Are Using ROPC Authentication Flow
id: 55695bc0-c8cf-461f-a379-2535f563c854
status: test
description: |
    Resource owner password credentials (ROPC) should be avoided if at all possible as this requires the user to expose their current password credentials to the application directly.
    The application then uses those credentials to authenticate the user against the identity provider.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#application-authentication-flows
author: <PERSON> '@markmorow', <PERSON> '@baileybercik'
date: 2022-06-01
tags:
    - attack.t1078
    - attack.defense-evasion
    - attack.persistence
    - attack.privilege-escalation
    - attack.initial-access
logsource:
    product: azure
    service: signinlogs
detection:
    selection:
        properties.message: ROPC
    condition: selection
falsepositives:
    - Applications that are being used as part of automated testing or a legacy application that cannot use any other modern authentication flow
level: medium
