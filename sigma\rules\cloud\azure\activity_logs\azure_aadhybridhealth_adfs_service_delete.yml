title: Azure Active Directory Hybrid Health AD FS Service Delete
id: 48739819-8230-4ee3-a8ea-e0289d1fb0ff
status: test
description: |
    This detection uses azureactivity logs (Administrative category) to identify the deletion of an Azure AD Hybrid health AD FS service instance in a tenant.
    A threat actor can create a new AD Health ADFS service and create a fake server to spoof AD FS signing logs.
    The health AD FS service can then be deleted after it is not longer needed via HTTP requests to Azure.
references:
    - https://o365blog.com/post/hybridhealthagent/
author: <PERSON> (Cyb3rWard0g), <PERSON>TR (Open Threat Research), MSTIC
date: 2021-08-26
modified: 2023-10-11
tags:
    - attack.defense-evasion
    - attack.t1578.003
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        CategoryValue: 'Administrative'
        ResourceProviderValue: 'Microsoft.ADHybridHealthService'
        ResourceId|contains: 'AdFederationService'
        OperationNameValue: 'Microsoft.ADHybridHealthService/services/delete'
    condition: selection
falsepositives:
    - Legitimate AAD Health AD FS service instances being deleted in a tenant
level: medium
