title: Shell Execution Of Process Located In Tmp Directory
id: 2fade0b6-7423-4835-9d4f-335b39b83867
status: test
description: Detects execution of shells from a parent process located in a temporary (/tmp) directory
references:
    - https://blogs.jpcert.or.jp/en/2023/05/gobrat.html
    - https://jstnk9.github.io/jstnk9/research/GobRAT-Malware/
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
author: <PERSON><PERSON><PERSON>, @Joseliyo_Jstnk
date: 2023-06-02
tags:
    - attack.execution
logsource:
    product: linux
    category: process_creation
detection:
    selection:
        ParentImage|startswith: '/tmp/'
        Image|endswith:
            - '/bash'
            - '/csh'
            - '/dash'
            - '/fish'
            - '/ksh'
            - '/sh'
            - '/zsh'
    condition: selection
falsepositives:
    - Unknown
level: high
