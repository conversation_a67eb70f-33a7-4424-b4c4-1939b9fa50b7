title: Possible Applocker Bypass
id: 82a19e3a-2bfe-4a91-8c0d-5d4c98fbb719
status: deprecated
description: Detects execution of executables that can be used to bypass Applocker whitelisting
references:
    - https://github.com/carnal0wnage/ApplicationWhitelistBypassTechniques/blob/b348846a3bd2ff45e3616d63a4c2b4426f84772c/TheList.txt
    - https://room362.com/post/2014/2014-01-16-application-whitelist-bypass-using-ieexec-dot-exe/
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1127.001/T1127.001.md
author: juju4
date: 2019/01/16
modified: 2022/11/03
tags:
    - attack.defense_evasion
    - attack.t1218.004
    - attack.t1218.009
    - attack.t1127.001
    - attack.t1218.005
    - attack.t1218   # no way to map 1:1, so the technique level is required
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        CommandLine|contains:
            - '\msdt.exe'
            - '\installutil.exe'
            - '\regsvcs.exe'
            - '\regasm.exe'
            #- '\regsvr32.exe'  # too many FPs, very noisy
            - '\msbuild.exe'
            - '\ieexec.exe'
            #- '\mshta.exe'
            #- '\csc.exe'
    condition: selection
falsepositives:
    - False positives depend on scripts and administrative tools used in the monitored environment
    - Using installutil to add features for .NET applications (primarily would occur in developer environments)
level: low
