title: Azure Point-to-site VPN Modified or Deleted
id: d9557b75-267b-4b43-922f-a775e2d1f792
status: test
description: Identifies when a Point-to-site VPN is Modified or Deleted.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON> @austinsonger
date: 2021-08-08
modified: 2022-08-23
tags:
    - attack.impact
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.NETWORK/P2SVPNGATEWAYS/WRITE
            - MICROSOFT.NETWORK/P2SVPNGATEWAYS/DELETE
            - MICROSOFT.NETWORK/P2SVPNGATEWAYS/RESET/ACTION
            - MICROSOFT.NETWORK/P2SVPNGATEWAYS/GENERATEVPNPROFILE/ACTION
            - MICROSOFT.NETWORK/P2SVPNGATEWAYS/DISCONNECTP2SVPNCONNECTIONS/ACTION
            - MICROSOFT.NETWORK/P2SVPNGATEWAYS/PROVIDERS/MICROSOFT.INSIGHTS/DIAGNOSTICSETTINGS/WRITE
    condition: selection
falsepositives:
    - Point-to-site VPN being modified or deleted may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Point-to-site VPN modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
