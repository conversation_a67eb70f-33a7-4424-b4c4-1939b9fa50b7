title: Persistence Via Sudoers Files
id: ddb26b76-4447-4807-871f-1b035b2bfa5d
status: test
description: Detects creation of sudoers file or files in "sudoers.d" directory which can be used a potential method to persiste privileges for a specific user.
references:
    - https://github.com/h3xduck/TripleCross/blob/1f1c3e0958af8ad9f6ebe10ab442e75de33e91de/apps/deployer.sh
author: <PERSON><PERSON><PERSON><PERSON> (Nextron Systems)
date: 2022-07-05
modified: 2022-12-31
tags:
    - attack.persistence
    - attack.t1053.003
logsource:
    product: linux
    category: file_event
detection:
    selection:
        TargetFilename|startswith: '/etc/sudoers.d/'
    condition: selection
falsepositives:
    - Creation of legitimate files in sudoers.d folder part of administrator work
level: medium
