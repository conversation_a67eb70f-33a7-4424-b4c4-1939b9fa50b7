name: "Promote Experimental Rules To Test"

on:
  #push:
  #  branches:
  #      - "*"
  schedule:
    - cron: "0 0 1 * *" # At 00:00 on day-of-month 1.
  
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  pull-master:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
    - name: Set up Python 3.11
      uses: actions/setup-python@v4.5.0
      with:
        python-version: 3.11
    - name: Execute Rule Promoter Script
      run: |
        pip install pySigma
        python tests/promote_rules_status.py
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        reviewers: nasbench, frack113, phantinuss
        delete-branch: true
        commit-message: 'chore: promote older rules status from `experimental` to `test`'
        branch: 'create-pull-request/rule-promotion'
        title: 'Promote Older Rules From `experimental` to `test`'
        body: |
          ### Summary of the Pull Request

          This PR promotes and upgrade the status of rules that haven't been changed for over 300 days from `experimental` to `test`

          ### Changelog

          chore: promote older rules status from `experimental` to `test`

          ### Example Log Event

          N/A

          ### Fixed Issues

          N/A

          ### SigmaHQ Rule Creation Conventions
          
          - If your PR adds new rules, please consider following and applying these [conventions](https://github.com/SigmaHQ/sigma-specification/blob/main/sigmahq/sigmahq_conventions.md)
