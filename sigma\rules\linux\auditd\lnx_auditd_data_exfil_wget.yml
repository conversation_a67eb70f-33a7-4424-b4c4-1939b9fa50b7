title: Data Exfiltration with Wget
id: cb39d16b-b3b6-4a7a-8222-1cf24b686ffc
status: test
description: |
    Detects attempts to post the file with the usage of wget utility.
    The adversary can bypass the permission restriction with the misconfigured sudo permission for wget utility which could allow them to read files like /etc/shadow.
references:
    - https://linux.die.net/man/1/wget
    - https://gtfobins.github.io/gtfobins/wget/
author: 'Pawel Mazur'
date: 2021-11-18
modified: 2022-12-25
tags:
    - attack.exfiltration
    - attack.t1048.003
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: EXECVE
        a0: wget
        a1|startswith: '--post-file='
    condition: selection
falsepositives:
    - Legitimate usage of wget utility to post a file
level: medium
