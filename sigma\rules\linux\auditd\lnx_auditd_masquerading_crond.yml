title: Masquerading as Linux Crond Process
id: 9d4548fa-bba0-4e88-bd66-5d5bf516cda0
status: test
description: |
  Masquerading occurs when the name or location of an executable, legitimate or malicious, is manipulated or abused for the sake of evading defenses and observation.
  Several different variations of this technique have been observed.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/8a82e9b66a5b4f4bc5b91089e9f24e0544f20ad7/atomics/T1036.003/T1036.003.md#atomic-test-2---masquerading-as-linux-crond-process
author: <PERSON><PERSON>, oscd.community
date: 2019-10-21
modified: 2023-08-22
tags:
    - attack.defense-evasion
    - attack.t1036.003
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'execve'
        a0: 'cp'
        a1: '/bin/sh'
        a2|endswith: '/crond'
    condition: selection
level: medium
