# Reference Archiver Results

Last Execution: 2025-07-15 02:20:07

### Archiver Script Results


#### Newly Archived References

N/A

#### Already Archived References

- https://github.com/gentilkiwi/mimikatz
- https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#suspicious-inbox-forwarding
- https://learn.microsoft.com/en-us/windows/client-management/client-tools/quick-assist#disable-quick-assist-within-your-organization
- https://www.cisa.gov/sites/default/files/2024-04/aa24-109a-stopransomware-akira-ransomware_2.pdf
- https://x.com/russianpanda9xx/status/1940831134759506029
- https://trustedsec.com/blog/adexplorer-on-engagements
- https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#azure-ad-threat-intelligence-user
- https://learn.microsoft.com/en-us/windows/win32/amsi/how-amsi-helps
- https://tria.ge/240307-1hlldsfe7t/behavioral2/analog?main_event=Registry&op=SetValueKeyInt
- https://boinc.berkeley.edu/
- https://learn.microsoft.com/en-us/windows/security/application-security/application-control/app-control-for-business/deployment/deploy-appcontrol-policies-with-script
- https://man.freebsd.org/cgi/man.cgi?pwd_mkdb
- https://developer.broadcom.com/xapis/esxcli-command-reference/7.0.0/namespace/esxcli_storage.html
- https://learn.microsoft.com/en-us/powershell/module/nettcpip/test-netconnection?view=windowsserver2022-ps
- https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-srvs/02b1f559-fda2-4ba3-94c2-806eb2777183
- https://github.com/splunk/security_content/blob/300af51b88ad5d5b27ce4f5f54e4d6e6a3a2c06d/detections/endpoint/office_spawning_control.yml
- https://twitter.com/th3_protoCOL/status/1480621526764322817
- https://www.splunk.com/en_us/blog/security/inno-setup-malware-redline-stealer-campaign.html
- https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/schtasks-create
- https://gtfobins.github.io/gtfobins/nawk/#shell

#### Error While Archiving References

- https://bazaar.abuse.ch/browse/tag/one/
- https://web.archive.org/web/20231210115125/http://www.xuetr.com/
- https://learn.microsoft.com/en-us/entra/id-governance/privileged-identity-management/pim-how-to-configure-security-alerts#roles-are-being-activated-too-frequently
- https://www.synacktiv.com/publications/ntlm-reflection-is-dead-long-live-ntlm-reflection-an-in-depth-analysis-of-cve-2025
- https://manual.cs50.io/2/personality
- https://www.virustotal.com/gui/file/****************************************************************
- https://www.hexacorn.com/blog/2020/08/23/odbcconf-lolbin-trifecta/
- https://github.com/0xthirteen/SharpMove/
- https://www.joesandbox.com/analysis/1467354/0/html
- https://app.any.run/tasks/fa99cedc-9d2f-4115-a08e-291429ce3692
- https://www.hexacorn.com/blog/2018/05/28/beyond-good-ol-run-key-part-78-2/
- https://learn.microsoft.com/en-us/windows/win32/debug/configuring-automatic-debugging
- https://www.softperfect.com/products/networkscanner/
- https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/dnscmd
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4625
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#application-credentials
- https://learn.microsoft.com/en-us/sql/t-sql/statements/drop-table-transact-sql?view=sql-server-ver16
- https://us-cert.cisa.gov/ncas/alerts/aa21-008a
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4741
- https://portswigger.net/daily-swig/vmware-horizon-under-attack-as-china-based-ransomware-group-targets-log4j-vulnerability
- https://learn.microsoft.com/en-us/windows/security/application-security/application-control/app-control-for-business/deployment/deploy-appcontrol-policies-using-group-policy
- https://blog.talosintelligence.com/uat-5647-romcom/
- https://www.hexacorn.com/blog/2024/01/01/1-little-known-secret-of-hdwwiz-exe/
- https://web.archive.org/web/20190710034152/https://github.com/zerosum0x0/CVE-2019-0708
- https://web.archive.org/web/20200601000524/https://cyberx-labs.com/blog/gangnam-industrial-style-apt-campaign-targets-korean-industrial-companies/
- https://learn.microsoft.com/en-us/entra/id-protection/howto-identity-protection-configure-mfa-policy
- https://learn.microsoft.com/fr-fr/windows-server/administration/windows-commands/fsutil-behavior
- https://www.cisa.gov/stopransomware/ransomware-guide
- https://man7.org/linux/man-pages/man2/mknod.2.html
- https://www.hexacorn.com/blog/2024/01/06/1-little-known-secret-of-fondue-exe/
- https://www.virustotal.com/gui/file/****************************************************************
- https://gtfobins.github.io/gtfobins/capsh/#shell
- https://localtonet.com/documents/supported-tunnels
- https://www.deepwatch.com/labs/customer-advisory-fortios-ssl-vpn-vulnerability-cve-2022-42475-exploited-in-the-wild/
- https://learn.microsoft.com/en-us/windows/security/application-security/application-control/windows-defender-application-control/applocker/what-is-applocker
- https://labs.watchtowr.com/expression-payloads-meet-mayhem-cve-2025-4427-and-cve-2025-4428/?123
- https://thehackernews.com/2024/12/cisa-and-fbi-raise-alerts-on-exploited.html
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts#assignment-and-elevation
- https://web.archive.org/web/**************/https://github.com/hhlxf/PrintNightmare/
- https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.utility/send-mailmessage?view=powershell-7.4
- https://www.hexacorn.com/blog/2013/12/08/beyond-good-ol-run-key-part-5/
- https://intel.thedfirreport.com/events/view/30032
- https://thedfirreport.com/2024/01/29/buzzing-on-christmas-eve-trigona-ransomware-in-3-hours/
- https://unit42.paloaltonetworks.com/snipbot-romcom-malware-variant/
- https://kubernetes.io/docs/reference/config-api/apiserver-audit.v1/
- https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-rprn/d42db7d5-f141-4466-8f47-0a4be14e2fc1
- https://tria.ge/241231-j9yatstqbm/behavioral1
- https://www.nccgroup.com/us/research-blog/lapsus-recent-techniques-tactics-and-procedures/
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-user-accounts#monitoring-for-failed-unusual-sign-ins
- https://thedfirreport.com/2024/04/29/from-icedid-to-dagon-locker-ransomware-in-29-days/
- https://learn.microsoft.com/en-us/sql/t-sql/statements/drop-database-transact-sql?view=sql-server-ver16
- https://symantec-enterprise-blogs.security.com/threat-intelligence/harvester-new-apt-attacks-asia
- https://thehackernews.com/2024/03/github-rolls-out-default-secret.html
- https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.security/get-acl?view=powershell-7.4
- https://cloud.google.com/access-context-manager/docs/audit-logging
- https://thedfirreport.com/2025/05/19/another-confluence-bites-the-dust-falling-to-elpaco-team-ransomware/
- https://tria.ge/220422-1pw1pscfdl/
- https://rapid7.com/blog/post/2019/02/19/stack-based-buffer-overflow-attacks-what-you-need-to-know/
- https://medium.com/r3d-buck3t/red-teaming-in-cloud-leverage-azure-frontdoor-cdn-for-c2-redirectors-79dd9ca98178
- https://www.trendmicro.com/content/dam/trendmicro/global/en/research/24/b/lockbit-attempts-to-stay-afloat-with-a-new-version/technical-appendix-lockbit-ng-dev-analysis.pdf
- https://news.sophos.com/en-us/2024/08/07/sophos-mdr-hunt-tracks-mimic-ransomware-campaign-against-organizations-in-india/
- https://www.optiv.com/blog/post-exploitation-using-netntlm-downgrade-attacks
- https://medium.com/@shaherzakaria8/downloading-trojan-lumma-infostealer-through-capatcha-1f25255a0e71
- https://strontic.github.io/xcyclopedia/library/mode.com-59D1ED51ACB8C3D50F1306FD75F20E99.html
- https://web.archive.org/web/20180203014709/https://blog.alsid.eu/dcshadow-explained-4510f52fc19d?gi=c426ac876c48
- https://learn.microsoft.com/en-us/troubleshoot/windows-server/networking/netsh-advfirewall-firewall-control-firewall-behavior
- https://social.technet.microsoft.com/wiki/contents/articles/7535.adfind-command-examples.aspx
- https://www.hexacorn.com/blog/2018/12/30/beyond-good-ol-run-key-part-98/
- https://docs.github.com/en/enterprise-cloud@latest/admin/monitoring-activity-in-your-enterprise/reviewing-audit-logs-for-your-enterprise/audit-log-events-for-your-enterprise#migration
- https://www.cyberciti.biz/faq/show-all-running-processes-in-linux/
- https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/systeminfo
- https://thehackernews.com/2025/03/unpatched-windows-zero-day-flaw.html
- https://unit42.paloaltonetworks.com/chromeloader-malware/
- https://thedfirreport.com/2024/04/01/from-onenote-to-ransomnote-an-ice-cold-intrusion/
- https://www.group-ib.com/resources/threat-research/silence_2.0.going_global.pdf
- https://www.hexacorn.com/blog/2018/08/31/beyond-good-ol-run-key-part-85/
- https://x.com/cyb3rops/status/1862406110365245506
- https://www.hexacorn.com/blog/2016/03/10/beyond-good-ol-run-key-part-36/
- https://www.dsinternals.com/en/dpapi-backup-key-theft-auditing/
- https://docs.github.com/en/repositories/creating-and-managing-repositories/transferring-a-repository
- https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-rprn/4464eaf0-f34f-40d5-b970-736437a21913
- https://lolbas-project.github.io/lolbas/OtherMSBinaries/Vshadow/
- https://github.com/pr0xylife/Pikabot/blob/fc58126127adf0f65e78f4eec59675523f48f086/Pikabot_22.12.2023.txt
- https://adsecurity.org/?p=1785
- https://www.cve.org/CVERecord?id=CVE-2024-1709
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4673
- https://posts.specterops.io/the-tale-of-settingcontent-ms-files-f1ea253e4d39
- https://www.loobins.io/binaries/xattr/
- https://news.ycombinator.com/item?id=29504755
- https://www.hexacorn.com/blog/2023/06/07/this-lolbin-doesnt-exist/
- http://www.hexacorn.com/blog/2018/08/16/squirrel-as-a-lolbin/
- https://www.trustedsec.com/blog/critical-vulnerability-in-progress-moveit-transfer-technical-analysis-and-recommendations/
- http://www.hexacorn.com/blog/2017/07/31/the-wizard-of-x-oppa-plugx-style/
- https://www.hexacorn.com/blog/2018/09/02/beyond-good-ol-run-key-part-86/
- https://www.kroll.com/en/insights/publications/cyber/cactus-ransomware-prickly-new-variant-evades-detection
- https://www.hexacorn.com/blog/2018/04/24/extexport-yet-another-lolbin/
- https://www.virustotal.com/gui/file/****************************************************************/behavior
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-user-accounts#monitoring-for-successful-unusual-sign-ins
- https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_arithmetic_operators?view=powershell-5.1
- https://thedfirreport.com/2025/02/24/confluence-exploit-leads-to-lockbit-ransomware/
- https://github.com/c3c/ADExplorerSnapshot.py
- https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/Delete%20K8S%20events/
- https://man7.org/linux/man-pages/man2/sysinfo.2.html
- https://www.cyberciti.biz/tips/linux-iptables-how-to-flush-all-rules.html
- https://www.pwndefend.com/2022/01/07/log4shell-exploitation-and-hunting-on-vmware-horizon-cve-2021-44228/
- https://learn.microsoft.com/en-us/windows/win32/shell/launch
- https://www.kernel.org/doc/html/v4.10/_sources/admin-guide/sysrq.txt
- https://docs.oracle.com/cd/E19683-01/816-4883/6mb2joatd/index.html
- https://securelist.com/key-group-ransomware-samples-and-telegram-schemes/114025/
- https://bazaar.abuse.ch/sample/7bde840c7e8c36dce4c3bac937bcf39f36a6f118001b406bfbbc25451ce44fb4/
- https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-wkst/55118c55-2122-4ef9-8664-0c1ff9e168f3
- https://www.virustotal.com/gui/file/****************************************************************
- https://cert.gov.ua/article/6284080
- https://catalyst.prodaft.com/public/report/inside-the-latest-espionage-campaign-of-nebulous-mantis
- https://www.hexacorn.com/blog/2017/01/18/beyond-good-ol-run-key-part-55/
- https://github.com/nasbench/EVTX-ETW-Resources/blob/f1b010ce0ee1b71e3024180de1a3e67f99701fe4/ETWProvidersManifests/Windows10/1903/W10_1903_Pro_20200714_18362.959/WEPExplorer/Microsoft-Windows-WindowsUpdateClient.xml
- https://gist.github.com/travisbgreen/82b68bac499edbe0b17dcbfa0c5c71b7
- https://darkatlas.io/blog/medusa-ransomware-group-opsec-failure
- https://www.hexacorn.com/blog/2018/04/20/kernel-hacking-tool-you-might-have-never-heard-of-xuetr-pchunter/
- https://web.archive.org/web/20220319032520/https://blog.redbluepurple.io/offensive-research/bypassing-injection-detection
- https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#malicious-ip-address
- https://learn.microsoft.com/en-us/windows-hardware/drivers/devtest/bcdedit--set
- https://nvd.nist.gov/vuln/detail/CVE-2025-30406
- https://github.com/huntresslabs/threat-intel/blob/3bad6b0fadfcca3ff5680923e74e39edd72f32da/2023/2023-04/20-PaperCut/win_susp_papercut_code_execution.yml
- https://medium.com/@NullByteWht/hacking-macos-how-to-dump-1password-keepassx-lastpass-passwords-in-plaintext-723c5b1c311b
- https://projectdiscovery.io/blog/crushftp-authentication-bypass
- https://media.defense.gov/2021/Jul/19/2002805003/-1/-1/1/CSA_CHINESE_STATE-SPONSORED_CYBER_TTPS.PDF
- https://learn.microsoft.com/en-us/windows/security/application-security/application-control/app-control-for-business/deployment/deploy-appcontrol-policies-with-memcm
- https://labs.nettitude.com/blog/introducing-sharpwsus/
- https://www.proofpoint.com/us/blog/threat-insight/serpent-no-swiping-new-backdoor-targets-french-entities-unique-attack-chain
- https://github.com/sadshade/veeam-creds/blob/6010eaf31ba41011b58d6af3950cffbf6f5cea32/Veeam-Get-Creds.ps1
- https://learn.microsoft.com/en-us/windows/win32/shell/shell-and-managed-code
- https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations#microsoftkubernetes
- https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.utility/invoke-expression?view=powershell-7.2
- https://decoded.avast.io/janvojtesek/raspberry-robins-roshtyak-a-little-lesson-in-trickery/
- https://megatools.megous.com/
- https://strontic.github.io/xcyclopedia/library/dialer.exe-0B69655F912619756C704A0BF716B61F.html
- https://www.huntress.com/blog/attacking-mssql-servers-pt-ii
- https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/wbadmin-delete-systemstatebackup
- https://web.archive.org/web/20230929023836/http://powershellhelp.space/commands/set-netfirewallrule-psv5.php
- https://www.intrinsec.com/alphv-ransomware-gang-analysis/?cn-reloaded=1
- https://ss64.com/osx/sw_vers.html
- https://www.hexacorn.com/blog/2017/07/31/the-wizard-of-x-oppa-plugx-style/
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2012-r2-and-2012/cc731935(v=ws.11)
- https://taggart-tech.com/evildeno/
- https://www.huntress.com/blog/attacking-mssql-servers
- https://web.archive.org/web/20170909091934/https://blog.binarydefense.com/reliably-detecting-pass-the-hash-through-event-log-analysis
- https://mrd0x.com/filefix-clickfix-alternative/
- https://thedfirreport.com/2025/01/27/cobalt-strike-and-a-pair-of-socks-lead-to-lockbit-ransomware/
- https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-srvs/accf23b0-0f57-441c-9185-43041f1b0ee9
- https://developer.apple.com/library/archive/documentation/MacOSX/Conceptual/BPSystemStartup/Chapters/StartupItems.html
- https://www.packetlabs.net/posts/scattered-spider-is-a-young-ransomware-gang-exploiting-large-corporations/
- https://learn.microsoft.com/en-us/entra/identity/monitoring-health/reference-audit-activities#core-directory
- https://blu.org/mhonarc/discuss/2001/04/msg00285.php
- https://www.loobins.io/binaries/nscurl/
- https://github.com/joaoviictorti/RustRedOps/tree/ce04369a246006d399e8c61d9fe0e6b34f988a49/Self_Deletion
- https://attackerkb.com/topics/k0EgiL9Psz/cve-2025-2825/rapid7-analysis
- https://thecyberexpress.com/ukraine-hit-by-meshagent-malware-campaign/
- https://intel.thedfirreport.com/eventReports/view/70
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#application-granted-highly-privileged-permissions
- https://www.linkedin.com/posts/kevin-beaumont-security_ive-been-assisting-a-few-orgs-hit-with-successful-activity-7268055739116445701-xxjZ/
- https://learn.microsoft.com/en-us/windows/win32/msi/event-logging
- https://web.archive.org/web/20221204161143/https://www.glitch-cat.com/p/green-lambert-and-attack
- https://googleprojectzero.blogspot.com/2021/10/using-kerberos-for-authentication-relay.html
- https://www.virustotal.com/gui/search/behaviour_processes%253A%2522C%253A%255C%255CWindows%255C%255CSysWOW64%255C%255Cmore.com%2522%2520behaviour_processes%253A%2522C%253A%255C%255CWindows%255C%255CMicrosoft.NET%255C%255CFramework%255C%255Cv4.0.30319%255C%255Cvbc.exe%2522/files
- https://github.com/redcanaryco/atomic-red-team/blob/5f866ca4517e837c4ea576e7309d0891e78080a8/atomics/T1040/T1040.md#atomic-test-16---powershell-network-sniffing
- https://twitter.com/standa_t/status/1808868985678803222
- https://www.cyberciti.biz/faq/how-force-kill-process-linux/
- https://github.com/okta/workflows-templates/blob/1164f0eb71ce47c9ddc7d850e9ab87b5a2b42333/workflows/suspicious_activity_reported/readme.md
- https://www.hexacorn.com/blog/2018/04/27/i-shot-the-sigverif-exe-the-gui-based-lolbin/
- https://www.group-ib.com/resources/threat-research/red-curl-2.html
- https://learn.microsoft.com/en-us/windows/security/application-security/application-control/windows-defender-application-control/operations/event-id-explanations
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4794
- https://github.com/FalconForceTeam/FalconFriday/blob/a9219dfcfd89836f34660223f47d766982bdce46/Discovery/ADWS_Connection_from_Unexpected_Binary-Win.md
- https://blog.morphisec.com/vmware-identity-manager-attack-backdoor
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-infrastructure
- https://symantec-enterprise-blogs.security.com/blogs/threat-intelligence/cicada-apt10-japan-espionage
- https://developer.broadcom.com/xapis/esxcli-command-reference/7.0.0/namespace/esxcli_system.html
- https://cert.gov.ua/article/6277849
- https://web.archive.org/web/20220205033028/https://twitter.com/PythonResponder/status/1385064506049630211
- https://www.fortiguard.com/psirt/FG-IR-22-398
- http://www.hexacorn.com/blog/2020/05/25/how-to-con-your-host/
- https://www.sentinelone.com/blog/from-the-front-linesunsigned-macos-orat-malware-gambles-for-the-win/
- https://www.zscaler.fr/blogs/security-research/threat-actors-exploit-cve-2017-11882-deliver-agent-tesla
- https://www.bleepingcomputer.com/news/security/centrestack-rce-exploited-as-zero-day-to-breach-file-sharing-servers/
- https://gtfobins.github.io/gtfobins/gawk/#shell
- https://informationsecuritybuzz.com/the-real-danger-behind-a-simple-windows-shortcut/
- https://www.trendmicro.com/en_us/research/25/c/socgholishs-intrusion-techniques-facilitate-distribution-of-rans.html
- https://github.com/xephora/Threat-Remediation-Scripts/tree/main/Threat-Track/CS_INSTALLER
- https://ss64.com/nt/set.html
- https://dear-territory-023.notion.site/WebDav-Share-Testing-e4950fa0c00149c3aa430d779b9b1d0f?pvs=4
- https://us-cert.cisa.gov/ncas/alerts/aa21-259a
- https://hopeness.medium.com/master-the-linux-mknod-command-a-comprehensive-guide-1c150a546aa8
- https://ipurple.team/2024/07/15/sharphound-detection/
- http://www.hexacorn.com/blog/2016/07/22/beyond-good-ol-run-key-part-42/
- https://www.attackiq.com/2023/09/20/emulating-rhysida/
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#service-principal-assigned-to-a-role
- https://research.ifcr.dk/certipy-4-0-esc9-esc10-bloodhound-gui-new-authentication-and-request-methods-and-more-7237d88061f7
- https://web.archive.org/web/20230329154538/https://blog.menasec.net/2019/07/interesting-difr-traces-of-net-clr.html
- https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.management/Start-Process?view=powershell-5.1&viewFallbackFrom=powershell-7
- https://github.com/ossec/ossec-hids/blob/f6502012b7380208db81f82311ad4a1994d39905/etc/rules/syslog_rules.xml
- https://thecyberexpress.com/rogue-rdp-files-used-in-ukraine-cyberattacks/
- https://github.com/redcanaryco/atomic-red-team/blob/75fa21076dcefa348a7521403cdd6bfc4e88623c/atomics/T1082/T1082.md
- https://app.any.run/tasks/9a8fd563-4c54-4d0a-9ad8-1fe08339cbc3/
- https://docs.microsoft.com/en-us/powershell/module/powershellwebaccess/install-pswawebapplication
- https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-instance-identity-roles.html
- https://www.vaadata.com/blog/what-is-command-injection-exploitations-and-security-best-practices/
- https://syedhasan010.medium.com/forensics-analysis-of-an-lnk-file-da68a98b8415
- https://learn.microsoft.com/en-us/sql/tools/sqlps-utility?view=sql-server-ver15
- https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcomputer?view=windowsserver2022-ps
- https://www.microsoft.com/en-us/security/blog/2024/05/15/threat-actors-misusing-quick-assist-in-social-engineering-attacks-leading-to-ransomware/
- https://www.security.com/threat-intelligence/medusa-ransomware-attacks
- https://ngrok.com/blog-post/new-ngrok-domains
- https://learn.microsoft.com/en-us/dotnet/framework/tools/regasm-exe-assembly-registration-tool
- https://paper.seebug.org/1495/
- https://x.com/Wietze/status/1933495426952421843
- https://globetech.biz/index.php/2023/05/19/evading-edr-by-dll-sideloading-in-csharp/
- https://medium.com/falconforce/soaphound-tool-to-collect-active-directory-data-via-adws-165aca78288c
- https://x.com/cyberfeeddigest/status/1887041526397587859
- https://learn.microsoft.com/de-de/sysinternals/downloads/adexplorer
- https://admx.help/?Category=Windows_10_2016&Policy=Microsoft.Policies.InternetExplorer::IZ_UNCAsIntranet
- https://github.com/nasbench/Misc-Research/blob/fc46f6da34ff7e0076da28fd3e66d6e1100f1c2f/ETW/Microsoft-Windows-SMBClient.md
- https://www.geeksforgeeks.org/how-to-kill-processes-on-the-linux-desktop-with-xkill/
- https://docs.github.com/en/enterprise-cloud@latest/code-security/secret-scanning/push-protection-for-repositories-and-organizations
- https://pentestlab.blog/2022/03/21/unconstrained-delegation/
- https://www.atomicredteam.io/atomic-red-team/atomics/T1562.002#atomic-test-8---modify-event-log-channel-access-permissions-via-registry---powershell
- https://web.archive.org/web/20230329170326/https://blog.menasec.net/2019/02/threat-hunting-21-procdump-or-taskmgr.html
- https://www.hexacorn.com/blog/2019/09/20/beyond-good-ol-run-key-part-116/
- https://docs.microsoft.com/en-us/sql/tools/bcp-utility
- https://www.sans.org/blog/defending-against-scattered-spider-and-the-com-with-cybercrime-intelligence/
- https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-drsr/f977faaa-673e-4f66-b9bf-48c640241d47?redirectedfrom=MSDN
- http://www.hexacorn.com/blog/2016/03/10/beyond-good-ol-run-key-part-36/
- https://tria.ge/240225-jlylpafb24/behavioral1/analog?main_event=Registry&op=SetValueKeyInt
- https://objective-see.org/blog/blog_0x6D.html
- https://www.lifars.com/wp-content/uploads/2022/01/GriefRansomware_Whitepaper-2.pdf
- https://www.reverse.it/sample/0b4ef455e385b750d9f90749f1467eaf00e46e8d6c2885c260e1b78211a51684?environmentId=100
- https://github.com/elastic/detection-rules/blob/v8.6.0/rules/integrations/aws/initial_access_via_system_manager.toml
- https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_execution_policies?view=powershell-7.4
- https://github.com/codewhitesec/SysmonEnte/blob/fe267690fcc799fbda15398243615a30451d9099/screens/1.png
- https://cloud.hacktricks.xyz/pentesting-cloud/aws-security/aws-privilege-escalation/aws-rds-privesc#rds-modifydbinstance
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-user-accounts#short-lived-accounts
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4776
- https://www.welivesecurity.com/2020/07/16/mac-cryptocurrency-trading-application-rebranded-bundled-malware/
- https://labs.jumpsec.com/tokensmith-bypassing-intune-compliant-device-conditional-access/
- https://learn.microsoft.com/en-us/iis/configuration/system.webserver/httplogging
- https://app.any.run/tasks/69c5abaa-92ad-45ba-8c53-c11e23e05d04/
- https://gtfobins.github.io/gtfobins/curl/
- https://any.run/report/6eea2773c1b4b5c6fb7c142933e220c96f9a4ec89055bf0cf54accdcde7df535/a407f006-ee45-420d-b576-f259094df091
- https://www.youtube.com/watch?v=uSYvHUVU8xY
- https://www.mcafee.com/blogs/other-blogs/mcafee-labs/agent-teslas-unique-approach-vbs-and-steganography-for-delivery-and-intrusion/
- https://learn.microsoft.com/en-us/windows/client-management/client-tools/quick-assist
- https://www.cisco.com/c/en/us/td/docs/ios/12_2sr/12_2sra/feature/guide/srmgtint.html#wp1127609
- https://www.spamhaus.org/reputation-statistics/cctlds/domains/
- https://support.microsoft.com/en-us/office/data-security-and-python-in-excel-33cc88a4-4a87-485e-9ff9-f35958278327
- https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#password-spray
- https://www.loobins.io/binaries/launchctl/
- https://www.hexacorn.com/blog/2020/02/02/settingsynchost-exe-as-a-lolbin
- https://www.howtogeek.com/137270/50-file-extensions-that-are-potentially-dangerous-on-windows
- https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-tsch/d1058a28-7e02-4948-8b8d-4a347fa64931
- https://web.archive.org/web/20211001064856/https://github.com/snovvcrash/DInjector
- https://tria.ge/231023-lpw85she57/behavioral2
- https://blog.appsecco.com/kubernetes-namespace-breakout-using-insecure-host-path-volume-part-1-b382f2a6e216
- https://threatbook.io/blog/Analysis-of-APT-C-60-Attack-on-South-Korea
- https://x.com/_st0pp3r_/status/1742203752361128162?s=20
- https://admx.help/?Category=Windows_10_2016&Policy=Microsoft.Policies.InternetExplorer::SecurityPage_AutoDetect
- https://sysdig.com/blog/detecting-and-mitigating-cve-2024-12084-rsync-remote-code-execution/
- https://research.nccgroup.com/2018/11/22/turla-png-dropper-is-back/
- https://www.aon.com/cyber-solutions/aon_cyber_labs/linux-based-inter-process-code-injection-without-ptrace2/
- https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/List%20K8S%20secrets/
- https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2025-49144
- https://learn.microsoft.com/en-us/windows/win32/api/shobjidl_core/nn-shobjidl_core-iexecutecommand
- https://github.com/logangoins/SharpSuccessor
- https://docs.connectwise.com/ConnectWise_Control_Documentation/Get_started/Host_client/View_menu/Backstage_mode
- https://docs.aws.amazon.com/IAM/latest/APIReference/API_DeleteSAMLProvider.html
- https://www.hexacorn.com/blog/2018/04/22/beyond-good-ol-run-key-part-76/
- https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#impossible-travel
- https://www.hexacorn.com/blog/2022/01/16/beyond-good-ol-run-key-part-135/
- https://github.com/redcanaryco/atomic-red-team/blob/c4097dc7ed14d7f7d08c89d148c4307097e8c294/atomics/T1486/T1486.md
- https://github.com/rtecCyberSec/BitlockMove
- https://app.any.run/tasks/ea944b89-69d8-49c8-ac1f-5c76ad300db2
- https://hijacklibs.net/entries/microsoft/built-in/mpsvc.html
- https://github.com/HackTricks-wiki/hacktricks/blob/e4c7b21b8f36c97c35b7c622732b38a189ce18f7/src/windows-hardening/windows-local-privilege-escalation/privilege-escalation-with-autorun-binaries.md
- https://msrc.microsoft.com/update-guide/en-US/vulnerability/CVE-2025-33053
- http://www.hexacorn.com/blog/2018/05/01/wab-exe-as-a-lolbin/
- https://github.com/CICADA8-Research/RemoteKrbRelay
- https://github.com/The-Viper-One/Invoke-PowerDPAPI/
- http://www.hexacorn.com/blog/2013/01/19/beyond-good-ol-run-key-part-3/
- https://tria.ge/240731-jh4crsycnb/behavioral2
- https://www.linkedin.com/feed/update/urn:li:ugcPost:7257437202706493443?commentUrn=urn%3Ali%3Acomment%3A%28ugcPost%3A7257437202706493443%2C7257522819985543168%29&dashCommentUrn=urn%3Ali%3Afsd_comment%3A%287257522819985543168%2Curn%3Ali%3AugcPost%3A7257437202706493443%29
- https://redcanary.com/blog/threat-intelligence/intelligence-insights-may-2025/
- https://learn.microsoft.com/en-us/sql/t-sql/statements/alter-server-audit-transact-sql?view=sql-server-ver16
- https://www.hexacorn.com/blog/2024/10/12/the-sweet16-the-oldbin-lolbin-called-setup16-exe/
- https://redcanary.com/blog/msix-installers/
- https://www.wiz.io/blog/how-to-set-secure-defaults-on-aws
- https://wazuh.com/blog/how-to-detect-meshagent-with-wazuh/
- https://strontic.github.io/xcyclopedia/library/vbc.exe-A731372E6F6978CE25617AE01B143351.html
- https://cloud.google.com/blog/topics/threat-intelligence/alphv-ransomware-backup/
- https://www.ihteam.net/advisory/terramaster-tos-multiple-vulnerabilities/
- https://learn.microsoft.com/en-us/sysinternals/downloads/autoruns
- https://www.hexacorn.com/blog/2019/02/15/beyond-good-ol-run-key-part-103/
- https://www.picussecurity.com/resource/blog/as-rep-roasting-attack-explained-mitre-attack-t1558.004
- https://man7.org/linux/man-pages/man2/syslog.2.html
- https://www.cyberciti.biz/faq/linux-remove-user-command/
- https://www.assetnote.io/resources/research/citrix-bleed-leaking-session-tokens-with-cve-2023-4966
- https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/Privileged%20container/
- http://www.hexacorn.com/blog/2020/02/05/stay-positive-lolbins-not/
- https://www.nextron-systems.com/2024/03/22/unveiling-kamikakabot-malware-analysis/
- http://www.hexacorn.com/blog/2019/03/30/sqirrel-packages-manager-as-a-lolbin-a-k-a-many-electron-apps-are-lolbins-by-default/
- https://manpages.debian.org/unstable/ecasound/ecasound.1.en.html
- https://learn.microsoft.com/en-us/defender-endpoint/troubleshoot-microsoft-defender-antivirus?view=o365-worldwide
- https://twitter.com/Kostastsale/status/1480716528421011458
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4616
- https://learn.microsoft.com/en-us/windows/win32/shell/app-registration
- https://goodworkaround.com/2022/02/15/digging-into-azure-ad-certificate-based-authentication/
- https://www.virustotal.com/gui/file/****************************************************************
- https://labs.watchtowr.com/palo-alto-putting-the-protecc-in-globalprotect-cve-2024-3400/
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-user-accounts#unusual-sign-ins
- https://learn.microsoft.com/en-us/azure/active-directory/hybrid/how-to-connect-monitor-federation-changes
- https://bazaar.abuse.ch/sample/5cb9876681f78d3ee8a01a5aaa5d38b05ec81edc48b09e3865b75c49a2187831/
- https://learn.microsoft.com/en-us/windows/client-management/mdm/policy-csp-windowsai#disableaidataanalysis
- https://github.com/clearvector/lambda-spy
- https://web.archive.org/web/**************/https://blog.menasec.net/2019/02/threat-hunting-5-detecting-enumeration.html
- https://web.archive.org/web/**************/https://answers.microsoft.com/en-us/protect/forum/mse-protect_scanning/microsoft-antimalware-has-removed-history-of/f15af6c9-01a9-4065-8c6c-3f2bdc7de45e
- https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#new-country
- https://outpost24.com/blog/crushftp-auth-bypass-vulnerability/
- https://docs.redhat.com/en/documentation/red_hat_enterprise_linux/4/html/reference_guide/s3-proc-sys-kernel
- https://github.com/CheraghiMilad/bypass-Neo23x0-auditd-config/blob/f1c478a37911a5447d5ffcd580f22b167bf3df14/sysinfo-syscall/README.md
- https://cloud.google.com/blog/topics/threat-intelligence/unc3944-targets-saas-applications
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-6423
- https://www.elastic.co/guide/en/security/current/unusual-file-modification-by-dns-exe.html
- https://www.vectra.ai/blog/undermining-microsoft-teams-security-by-mining-tokens
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2008-R2-and-2008/dd299871(v=ws.10)
- https://learn.microsoft.com/en-us/troubleshoot/windows-client/installing-updates-features-roles/system-registry-no-backed-up-regback-folder
- https://blog.sekoia.io/scattered-spider-laying-new-eggs/
- https://www.hexacorn.com/blog/2013/01/19/beyond-good-ol-run-key-part-3/
- https://mp.weixin.qq.com/s/wUoBy7ZiqJL2CUOMC-8Wdg
- https://www.fireeye.com/blog/threat-research/2020/01/saigon-mysterious-ursnif-fork.html
- https://medium.com/@msuiche/the-nsa-compromised-swift-network-50ec3000b195
- https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/takeown
- https://learn.microsoft.com/en-us/windows/win32/setupapi/run-and-runonce-registry-keys
- https://research.checkpoint.com/2025/stealth-falcon-zero-day/
- https://www.hexacorn.com/blog/2018/04/23/beyond-good-ol-run-key-part-77/
- https://www.huntress.com/blog/moveit-transfer-critical-vulnerability-rapid-response
- https://www.aon.com/cyber-solutions/aon_cyber_labs/yours-truly-signed-av-driver-weaponizing-an-antivirus-driver/
- https://gtfobins.github.io/gtfobins/gcc/#shell
- https://github.com/splunk/security_content/blob/7283ba3723551f46b69dfeb23a63b358afb2cb0e/lookups/browser_app_list.csv?plain=1
- https://developer.broadcom.com/xapis/esxcli-command-reference/7.0.0/namespace/esxcli_vm.html
- https://research.nccgroup.com/2018/03/10/apt15-is-alive-and-strong-an-analysis-of-royalcli-and-royaldns/
- https://www.huntress.com/blog/fake-browser-updates-lead-to-boinc-volunteer-computing-software
- https://www.virustotal.com/gui/file/****************************************************************
- https://x.com/Max_Mal_/status/1826179497084739829
- https://learn.microsoft.com/en-us/dotnet/api/system.directoryservices.accountmanagement?view=net-8.0
- https://gtfobins.github.io/gtfobins/rsync/#shell
- https://community.openvpn.net/openvpn/wiki/ManagingWindowsTAPDrivers
- https://www.cisa.gov/known-exploited-vulnerabilities-catalog
- https://github.com/Ylianst/MeshAgent/blob/52cf129ca43d64743181fbaf940e0b4ddb542a37/modules/win-dispatcher.js#L173
- https://github.com/wietze/HijackLibs/tree/dc9c9f2f94e6872051dab58fbafb043fdd8b4176/yml/3rd_party/python
- https://unicornofhunt.com/2025/05/22/When-Unicorns-Go-Quiet-BITS-Jobs-and-the-Art-of-Stealthy-Transfers/
- https://www.group-ib.com/blog/apt41-world-tour-2021/
- https://www.virustotal.com/gui/search/behaviour_network%253A*.miningocean.org/files
- https://web.archive.org/web/**************/https://githubmemory.com/repo/FunctFan/JNDIExploit
- https://github.com/CheraghiMilad/bypass-Neo23x0-auditd-config/blob/f1c478a37911a5447d5ffcd580f22b167bf3df14/personality-syscall/README.md
- https://www.huntress.com/blog/know-thy-enemy-a-novel-november-case-on-persistent-remote-access
- https://tria.ge/240521-ynezpagf56/behavioral1
- https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/assoc
- https://learn.microsoft.com/en-us/windows/compatibility/ntvdm-and-16-bit-app-support
- https://web.archive.org/web/20190508165435/https://www.operationblockbuster.com/wp-content/uploads/2016/02/Operation-Blockbuster-RAT-and-Staging-Report.pdf
- https://labs.yarix.com/2025/06/doppelganger-an-advanced-lsass-dumper-with-process-cloning/
- https://www.hexacorn.com/blog/2023/12/26/1-little-known-secret-of-runonce-exe-32-bit/
- https://intel.thedfirreport.com/eventReports/view/57
- https://tria.ge/231212-r1bpgaefar/behavioral2
- https://github.com/vari-sh/RedTeamGrimoire/tree/b5e7635d34db6e1f0398d8847e8f293186e947c5/HollowReaper
- https://learn.microsoft.com/en-us/previous-versions/dotnet/framework/data/wcf/wcf-data-service-client-utility-datasvcutil-exe
- https://learn.microsoft.com/en-us/dotnet/api/system.diagnostics.eventing.reader.eventlogsession.clearlog?view=windowsdesktop-9.0&viewFallbackFrom=dotnet-plat-ext-5.0#System_Diagnostics_Eventing_Reader_EventLogSession_ClearLog_System_String_
- https://web.archive.org/web/20230217071802/https://blooteem.com/march-2022
- https://github.com/grayhatkiller/SharpExShell
- https://learn.microsoft.com/en-us/windows/package-manager/winget/install#local-install
- https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#atypical-travel
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#appid-uri-added-modified-or-removed
- https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#malware-linked-ip-address-deprecated
- https://redcanary.com/blog/threat-intelligence/intelligence-insights-october-2024/
- https://cloud.google.com/logging/docs/audit/understanding-audit-logs
- https://beierle.win/2024-12-20-Weaponizing-WDAC-Killing-the-Dreams-of-EDR/
- http://www.hexacorn.com/blog/2017/05/01/running-programs-via-proxy-jumping-on-a-edr-bypass-trampoline/
- https://github.com/redcanaryco/atomic-red-team/blob/master/atomics/T1490/T1490.md#atomic-test-12---disable-time-machine
- https://fourcore.io/blogs/threat-hunting-browser-credential-stealing
- https://github.com/splunk/security_content/blob/88d689fe8a055d8284337b9fad5d9152b42043db/detections/endpoint/petitpotam_suspicious_kerberos_tgt_request.yml
- https://x.com/NullSecurityX/status/1937444064867029179
- https://cloud.google.com/blog/topics/threat-intelligence/apt41-innovative-tactics
- https://blackpointcyber.com/blog/racing-to-exploit-centrestacks-cve-2025-30406/
- https://github.com/redcanaryco/atomic-red-team/blob/75fa21076dcefa348a7521403cdd6bfc4e88623c/atomics/T1124/T1124.md
- https://stackoverflow.com/questions/66011412/how-to-clear-a-event-log-in-powershell-7
- https://github.com/vari-sh/RedTeamGrimoire/tree/668e0357072546065729ad623f8c02f7be21bb08/Doppelganger
- https://www.trendmicro.com/en_us/research/25/c/windows-shortcut-zero-day-exploit.html
- https://www.giac.org/paper/gcih/266/review-ftp-protocol-cyber-defense-initiative/102802
- https://github.com/EvotecIT/TheDashboard/blob/481a9ce8f82f2fd55fe65220ee6486bae6df0c9d/Examples/RunReports/PingCastle.ps1
- https://web.archive.org/web/20230331181619/https://blog.dylan.codes/evading-sysmon-and-windows-event-logging/
- https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
- https://www.linkedin.com/posts/huntress-labs_when-a-sketchy-incident-hits-your-network-activity-7304940371078238208-Th_l/?utm_source=share&utm_medium=member_desktop&rcm=ACoAAAJTlRcB28IaUtg03HUU-IdliwzoAL1flGc
- https://twitter.com/th3_protoCOL/status/1536788652889497600
- https://github.com/rapid7/metasploit-framework/issues/11337
- https://github.com/nasbench/Misc-Research/blob/8ee690e43a379cbce8c9d61107442c36bd9be3d3/Other/Undocumented-Flags-Sdbinst.md
- https://www.elastic.co/guide/en/security/current/potential-non-standard-port-ssh-connection.html
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-5038
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4732
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4661
- https://github.com/DrorDvash/CVE-2022-22954_VMware_PoC
- https://www.virustotal.com/gui/file/****************************************************************/behavior
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-devices#non-compliant-device-sign-in
- https://app.any.run/tasks/1efb3ed4-cc0f-4690-a0ed-24516809bc72/
- https://www.microsoft.com/en-us/security/blog/2022/12/12/iis-modules-the-evolution-of-web-shells-and-how-to-detect-them/
- https://www.splunk.com/en_us/blog/security/threat-update-awfulshred-script-wiper.html
- https://www.welivesecurity.com/2020/03/05/guildma-devil-drives-electric/
- https://learn.microsoft.com/en-us/sql/t-sql/statements/truncate-table-transact-sql?view=sql-server-ver16
- https://www.hexacorn.com/blog/2023/12/31/1-little-known-secret-of-forfiles-exe/
- https://web.archive.org/web/20231230220738/https://www.lunasec.io/docs/blog/log4j-zero-day/
- https://github.com/dsnezhkov/TruffleSnout/blob/7c2f22e246ef704bc96c396f66fa854e9ca742b9/TruffleSnout/Docs/USAGE.md
- https://tria.ge/240226-fhbe7sdc39/behavioral1
- https://redcanary.com/blog/threat-detection/process-masquerading/
- https://blog.eclecticiq.com/china-nexus-nation-state-actors-exploit-sap-netweaver-cve-2025-31324-to-target-critical-infrastructures
- https://doublepulsar.com/kaseya-supply-chain-attack-delivers-mass-ransomware-event-to-us-companies-76e4ec6ec64b
- https://x.com/Threatlabz/status/1879956781360976155
- https://nvd.nist.gov/vuln/detail/CVE-2025-2825
- https://gist.github.com/MHaggis/7e67b659af9148fa593cf2402edebb41
- https://www.cyberciti.biz/faq/xclip-linux-insert-files-command-output-intoclipboard/
- https://learn.microsoft.com/en-us/entra/architecture/security-operations-devices#bitlocker-key-retrieval
- https://www.youtube.com/watch?v=52tAmVLg1KM&t=2070s
- https://learn.microsoft.com/en-us/mem/intune/apps/intune-management-extension
- https://learn.microsoft.com/en-us/windows/win32/vss/vshadow-tool-and-sample
- https://web.archive.org/web/20200530031906/https://techtalk.pcmatic.com/2017/11/30/running-dll-files-malware-analysis/
- https://learn.microsoft.com/en-us/iis/configuration/system.applicationhost/sites/sitedefaults/logfile/
- https://github.com/varwara/CVE-2024-35250
- https://github.com/logangoins/Krueger/tree/main
- https://www.hexacorn.com/blog/2017/01/14/beyond-good-ol-run-key-part-53/
- https://www.group-ib.com/blog/cve-2023-38831-winrar-zero-day/
- https://research.checkpoint.com/2023/rhadamanthys-v0-5-0-a-deep-dive-into-the-stealers-components/
- https://www.sentinelone.com/labs/ranzy-ransomware-better-encryption-among-new-features-of-thunderx-derivative/
- https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/set_1
- https://www.malwarebytes.com/blog/detections/pum-optional-nodispbackgroundpage
- https://www.loobins.io/binaries/pbpaste/
- https://learn.microsoft.com/en-us/windows/win32/api/minidumpapiset/nf-minidumpapiset-minidumpwritedump
- https://github.com/fortra/impacket/blob/ff8c200fd040b04d3b5ff05449646737f836235d/examples/secretsdump.py
- https://github.com/LOLBAS-Project/LOLBAS/blob/2cc01b01132b5c304027a658c698ae09dd6a92bf/yml/OSBinaries/Wbadmin.yml
- https://gladinetsupport.s3.us-east-1.amazonaws.com/gladinet/securityadvisory-cve-2005.pdf
- https://asec.ahnlab.com/en/40263/
- https://man7.org/linux/man-pages/man1/dmesg.1.html
- https://research.checkpoint.com/2024/raspberry-robin-keeps-riding-the-wave-of-endless-1-days/
- https://man7.org/linux/man-pages/man2/personality.2.html
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2008-r2-and-2008/dd364427(v=ws.10)
- https://cloud.google.com/blog/topics/threat-intelligence/cybercriminals-weaponize-fake-ai-websites
- https://www.huntress.com/blog/blackcat-ransomware-affiliate-ttps
- https://learn.microsoft.com/en-us/powershell/module/dism/enable-windowsoptionalfeature?view=windowsserver2022-ps
- https://www.fortalicesolutions.com/posts/hiding-behind-the-front-door-with-azure-domain-fronting
- https://www.hexacorn.com/blog/2013/09/19/beyond-good-ol-run-key-part-4/
- https://medium.com/system-weakness/detecting-as-rep-roasting-attacks-b5b3965f9714
- https://www.cyberciti.biz/faq/linux-hide-processes-from-other-users/
- https://learn.microsoft.com/en-us/dotnet/api/system.diagnostics.eventlog.clear
- https://cloud.google.com/blog/topics/threat-intelligence/evolution-of-fin7/
- https://f5-sdk.readthedocs.io/en/latest/apidoc/f5.bigip.tm.util.html#module-f5.bigip.tm.util.bash
- https://www.sans.org/blog/protecting-privileged-domain-accounts-lm-hashes-the-good-the-bad-and-the-ugly/
- https://learn.microsoft.com/en-us/defender-endpoint/attack-surface-reduction-rules-reference?view=o365-worldwide#block-process-creations-originating-from-psexec-and-wmi-commands
- https://www.elastic.co/guide/en/security/current/linux-restricted-shell-breakout-via-linux-binary-s.html
- https://learn.microsoft.com/en-us/visualstudio/debugger/debug-using-the-just-in-time-debugger?view=vs-2019
- https://www.bleepingcomputer.com/news/security/hackers-exploit-windows-smartscreen-flaw-to-drop-darkgate-malware/
- https://docs.github.com/en/enterprise-cloud@latest/admin/monitoring-activity-in-your-enterprise/reviewing-audit-logs-for-your-enterprise/audit-log-events-for-your-enterprise#ssh_certificate_authority
- https://www.splunk.com/en_us/blog/security/you-bet-your-lsass-hunting-lsass-access.html
- https://learn.microsoft.com/en-us/iis/manage/provisioning-and-managing-iis/configure-logging-in-iis
- https://web.archive.org/web/**************/https://github.com/cube0x0/CVE-2021-36934
- https://www.hexacorn.com/blog/2015/01/13/beyond-good-ol-run-key-part-24/
- https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.management/reset-computermachinepassword?view=powershell-5.1
- https://web.archive.org/web/20220830134315/https://content.fireeye.com/apt-41/rpt-apt41/
- https://www.anyviewer.com/help/remote-technical-support.html
- https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-10/security/threat-protection/auditing/event-4771
