title: Potential JNDI Injection Exploitation In JVM Based Application
id: bb0e9cec-d4da-46f5-997f-22efc59f3dca
status: test
description: Detects potential JNDI Injection exploitation. Often coupled with Log4Shell exploitation.
references:
    - https://www.wix.engineering/post/threat-and-vulnerability-hunting-with-application-server-error-logs
    - https://secariolabs.com/research/analysing-and-reproducing-poc-for-log4j-2-15-0
author: <PERSON><PERSON>
date: 2023-02-11
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: jvm
    definition: 'Requirements: application error logs must be collected (with LOG_LEVEL=ERROR and above)'
detection:
    keywords:
        - 'com.sun.jndi.ldap.'
        - 'org.apache.logging.log4j.core.net.JndiManager'
    condition: keywords
falsepositives:
    - Application bugs
level: high
