title: Accessing WinAPI in PowerShell for Credentials Dumping
id: 3f07b9d1-2082-4c56-9277-613a621983cc
status: deprecated
description: Detects Accessing to lsass.exe by Powershell
references:
    - https://speakerdeck.com/heirhabarov/hunting-for-powershell-abuse
author: oscd.community, <PERSON>
date: 2020/10/06
modified: 2022/12/18
tags:
    - attack.credential_access
    - attack.t1003.001
logsource:
    product: windows
    service: sysmon
detection:
    selection:
        EventID:
            - 8
            - 10
        SourceImage|endswith:
            - '\powershell.exe'
            - '\pwsh.exe'
        TargetImage|endswith: '\lsass.exe'
    condition: selection
falsepositives:
    - Unknown
level: high
