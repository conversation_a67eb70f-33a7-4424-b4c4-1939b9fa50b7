title: Antivirus Relevant File Paths Alerts
id: c9a88268-0047-4824-ba6e-4d81ce0b907c
status: test
description: |
    Detects an Antivirus alert in a highly relevant file path or with a relevant file name.
    This event must not be ignored just because the AV has blocked the malware but investigate, how it came there in the first place.
references:
    - https://www.nextron-systems.com/?s=antivirus
author: <PERSON><PERSON><PERSON> (Nextron Systems), <PERSON><PERSON><PERSON>
date: 2018-09-09
modified: 2024-11-02
tags:
    - attack.resource-development
    - attack.t1588
logsource:
    category: antivirus
detection:
    selection_path:
        Filename|contains:
            - ':\PerfLogs\'
            - ':\Temp\'
            - ':\Users\Default\'
            - ':\Users\Public\'
            - ':\Windows\'
            - '/www/'
            # - '\Client\'
            - '\inetpub\'
            - '\tsclient\'
            - 'apache'
            - 'nginx'
            - 'tomcat'
            - 'weblogic'
    selection_ext:
        Filename|endswith:
            - '.asax'
            - '.ashx'
            - '.asmx'
            - '.asp'
            - '.aspx'
            - '.bat'
            - '.cfm'
            - '.cgi'
            - '.chm'
            - '.cmd'
            - '.dat'
            - '.ear'
            - '.gif'
            - '.hta'
            - '.jpeg'
            - '.jpg'
            - '.jsp'
            - '.jspx'
            - '.lnk'
            - '.msc'
            - '.php'
            - '.pl'
            - '.png'
            - '.ps1'
            - '.psm1'
            - '.py'
            - '.pyc'
            - '.rb'
            - '.scf'
            - '.sct'
            - '.sh'
            - '.svg'
            - '.txt'
            - '.vbe'
            - '.vbs'
            - '.war'
            - '.wll'
            - '.wsf'
            - '.wsh'
            - '.xll'
            - '.xml'
    condition: 1 of selection_*
falsepositives:
    - Unlikely
level: high
