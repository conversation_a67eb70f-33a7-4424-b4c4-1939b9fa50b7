title: Application Whitelisting Bypass via DLL Loaded by odbcconf.exe
id: 65d2be45-8600-4042-b4c0-577a1ff8a60e
status: deprecated
description: Detects defence evasion attempt via odbcconf.exe execution to load DLL
references:
    - https://lolbas-project.github.io/lolbas/Binaries/Odbcconf/
    - https://twitter.com/Hexacorn/status/1187143326673330176
    - https://redcanary.com/blog/raspberry-robin/
    - https://github.com/elastic/protections-artifacts/commit/746086721fd385d9f5c6647cada1788db4aea95f#diff-94a1964b682707e4e3f77dd61a3bfface5401d08d8cf81145f388e09614aceca
author: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, oscd.community
date: 2019/10/25
modified: 2023/05/22
tags:
    - attack.defense_evasion
    - attack.t1218.008
logsource:
    category: process_creation
    product: windows
detection:
    selection_1_img:
        - Image|endswith: '\odbcconf.exe'
        - OriginalFileName: 'odbcconf.exe'
    selection_1_cli:
        CommandLine|contains:
            - '-a'
            - '-f'
            - '/a'
            - '/f'
            - 'regsvr'
    selection_2_parent:
        ParentImage|endswith: '\odbcconf.exe'
    selection_2_img:
        - Image|endswith: '\rundll32.exe'
        - OriginalFileName: 'RUNDLL32.EXE'
    condition: all of selection_1_* or all of selection_2_*
falsepositives:
    - Legitimate use of odbcconf.exe by legitimate user
level: medium
