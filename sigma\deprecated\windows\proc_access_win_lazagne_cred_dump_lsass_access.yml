title: Credential Dumping by LaZagne
id: 4b9a8556-99c4-470b-a40c-9c8d02c77ed0
status: stable
description: Detects LSASS process access by <PERSON><PERSON>agne for credential dumping.
references:
    - https://twitter.com/bh4b3sh/status/1303674603819081728
author: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
date: 2020/09/09
modified: 2022/08/13
tags:
    - attack.credential_access
    - attack.t1003.001
    - attack.s0349
logsource:
    category: process_access
    product: windows
detection:
    selection:
        TargetImage|endswith: '\lsass.exe'
        CallTrace|contains|all:
            - 'C:\Windows\SYSTEM32\ntdll.dll+'
            - '|C:\Windows\System32\KERNELBASE.dll+'
            - '_ctypes.pyd+'
            - 'python27.dll+'
        GrantedAccess: '0x1FFFFF'
    condition: selection
falsepositives:
    - Unknown
level: critical
