title: Potential PHP Reverse Shell
id: c6714a24-d7d5-4283-a36b-3ffd091d5f7e
status: test
description: |
    Detects usage of the PHP CLI with the "-r" flag which allows it to run inline PHP code. The rule looks for calls to the "fsockopen" function which allows the creation of sockets.
    Attackers often leverage this in combination with functions such as "exec" or "fopen" to initiate a reverse shell connection.
references:
    - https://pentestmonkey.net/cheat-sheet/shells/reverse-shell-cheat-sheet
    - https://www.revshells.com/
author: '@d4ns4n_'
date: 2023-04-07
tags:
    - attack.execution
logsource:
    category: process_creation
    product: linux
detection:
    selection:
        Image|contains: '/php'
        CommandLine|contains|all:
            - ' -r '
            - 'fsockopen'
        CommandLine|contains:
            - 'ash'
            - 'bash'
            - 'bsh'
            - 'csh'
            - 'ksh'
            - 'pdksh'
            - 'sh'
            - 'tcsh'
            - 'zsh'
    condition: selection
falsepositives:
    - Unknown
level: high
