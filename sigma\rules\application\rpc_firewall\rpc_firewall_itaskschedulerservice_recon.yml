title: Remote Schedule Task Recon via ITaskSchedulerService
id: 7f7c49eb-2977-4ac8-8ab0-ab1bae14730e
status: test
description: Detects remote RPC calls to read information about scheduled tasks
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-tsch/d1058a28-7e02-4948-8b8d-4a347fa64931
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-TSCH.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, <PERSON><PERSON>
date: 2022-01-01
tags:
    - attack.discovery
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:86d35949-83c9-4044-b424-db363231fd0c"'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 86d35949-83c9-4044-b424-db363231fd0c
    filter:
        OpNum:
            - 1
            - 3
            - 4
            - 10
            - 11
            - 12
            - 13
            - 14
            - 15
    condition: selection and not filter
falsepositives:
    - Unknown
level: high
