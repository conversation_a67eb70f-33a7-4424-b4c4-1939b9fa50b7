title: Kubernetes Events Deleted
id: 3132570d-cab2-4561-9ea6-1743644b2290
related:
    - id: 225d8b09-e714-479c-a0e4-55e6f29adf35
      type: derived
status: test
description: |
    Detects when events are deleted in Kubernetes.
    An adversary may delete Kubernetes events in an attempt to evade detection.
references:
    - https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/Delete%20K8S%20events/
author: <PERSON> (@laripping)
date: 2024-03-26
tags:
    - attack.defense-evasion
    - attack.t1070
logsource:
    category: application
    product: kubernetes
    service: audit
detection:
    selection:
        verb: 'delete'
        objectRef.resource: 'events'
    condition: selection
falsepositives:
    - Unknown
level: medium
