title: Azure Network Security Configuration Modified or Deleted
id: d22b4df4-5a67-4859-a578-8c9a0b5af9df
status: test
description: Identifies when a network security configuration is modified or deleted.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON> @austinsonger
date: 2021-08-08
modified: 2022-08-23
tags:
    - attack.impact
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.NETWORK/NETWORKSECURITYGROUPS/WRITE
            - MICROSOFT.NETWORK/NETWORKSECURITYGROUPS/DELETE
            - MICROSOFT.NETWORK/NETWORKSECURITYGROUPS/SECURITYRULES/WRITE
            - MICROSOFT.NETWORK/NETWORKSECURITYGROUPS/SECURITYRULES/DELETE
            - MICROSOFT.NETWORK/NETWORKSECURITYGROUPS/JOIN/ACTION
            - MICROSOFT.NETWORK/NETWORKSECURITYGROUPS/PROVIDERS/MICROSOFT.INSIGHTS/DIAGNOSTICSETTINGS/WRITE
    condition: selection
falsepositives:
    - Network Security Configuration being modified or deleted may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Network Security Configuration modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
