title: Potential SpEL Injection In Spring Framework
id: e9edd087-89d8-48c9-b0b4-5b9bb10896b8
status: test
description: Detects potential SpEL Injection exploitation, which may lead to RCE.
references:
    - https://owasp.org/www-community/vulnerabilities/Expression_Language_Injection
    - https://www.wix.engineering/post/threat-and-vulnerability-hunting-with-application-server-error-logs
author: <PERSON><PERSON>
date: 2023-02-11
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: spring
    definition: 'Requirements: application error logs must be collected (with LOG_LEVEL=ERROR and above)'
detection:
    keywords:
        - 'org.springframework.expression.ExpressionException'
    condition: keywords
falsepositives:
    - Application bugs
level: high
