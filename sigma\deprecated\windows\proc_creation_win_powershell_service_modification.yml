title: Stop Or Remove Antivirus Service
id: 6783aa9e-0dc3-49d4-a94a-8b39c5fd700b
status: deprecated
description: |
    Detects usage of 'Stop-Service' or 'Remove-Service' powershell cmdlet to disable AV services.
    Adversaries may disable security tools to avoid possible detection of their tools and activities by stopping antivirus service
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1562.001/T1562.001.md
    - https://research.nccgroup.com/2022/08/19/back-in-black-unlocking-a-lockbit-3-0-ransomware-attack/
author: frack113
date: 2021/07/07
modified: 2023/03/04
tags:
    - attack.defense_evasion
    - attack.t1562.001
logsource:
    category: process_creation
    product: windows
detection:
    selection_action:
        CommandLine|contains:
            - 'Stop-Service '
            - 'Remove-Service '
    selection_product:
        CommandLine|contains:
            # Feel free to add more service name
            - ' McAfeeDLPAgentService'
            - ' Trend Micro Deep Security Manager'
            - ' TMBMServer'
            - 'Sophos'
            - 'Symantec'
    condition: all of selection*
fields:
    - ComputerName
    - User
    - CommandLine
    - ParentCommandLine
falsepositives:
    - Unknown
level: high
