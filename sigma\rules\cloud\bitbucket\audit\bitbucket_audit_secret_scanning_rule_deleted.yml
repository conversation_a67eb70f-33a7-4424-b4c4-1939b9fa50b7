title: Bitbucket Secret Scanning Rule Deleted
id: ff91e3f0-ad15-459f-9a85-1556390c138d
status: test
description: Detects when secret scanning rule is deleted for the project or repository.
references:
    - https://confluence.atlassian.com/bitbucketserver/audit-log-events-776640423.html
    - https://confluence.atlassian.com/bitbucketserver/secret-scanning-1157471613.html
author: <PERSON> (@faisalusuf)
date: 2024-02-25
tags:
    - attack.defense-evasion
    - attack.t1562.001
logsource:
    product: bitbucket
    service: audit
    definition: 'Requirements: "Basic" log level is required to receive these audit events.'
detection:
    selection:
        auditType.category:
            - 'Projects'
            - 'Repositories'
        auditType.action:
            - 'Project secret scanning rule deleted'
            - 'Repository secret scanning rule deleted'
    condition: selection
falsepositives:
    - Legitimate user activity.
level: low
