title: AWS Suspicious SAML Activity
id: f43f5d2f-3f2a-4cc8-b1af-81fde7dbaf0e
status: test
description: Identifies when suspicious SAML activity has occurred in AWS. An adversary could gain backdoor access via SAML.
references:
    - https://docs.aws.amazon.com/IAM/latest/APIReference/API_UpdateSAMLProvider.html
    - https://docs.aws.amazon.com/STS/latest/APIReference/API_AssumeRoleWithSAML.html
author: <PERSON>
date: 2021-09-22
modified: 2022-12-18
tags:
    - attack.initial-access
    - attack.t1078
    - attack.lateral-movement
    - attack.t1548
    - attack.privilege-escalation
    - attack.t1550
    - attack.t1550.001
logsource:
    product: aws
    service: cloudtrail
detection:
    selection_sts:
        eventSource: 'sts.amazonaws.com'
        eventName: 'AssumeRoleWithSAML'
    selection_iam:
        eventSource: 'iam.amazonaws.com'
        eventName: 'UpdateSAMLProvider'
    condition: 1 of selection_*
falsepositives:
    - Automated processes that uses Terraform may lead to false positives.
    - SAML Provider could be updated by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - SAML Provider being updated from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
