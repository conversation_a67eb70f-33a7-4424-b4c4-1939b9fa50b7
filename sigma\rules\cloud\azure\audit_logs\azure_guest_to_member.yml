title: User State Changed From Guest To Member
id: 8dee7a0d-43fd-4b3c-8cd1-605e189d195e
status: test
description: Detects the change of user type from "Guest" to "Member" for potential elevation of privilege.
references:
    - https://learn.microsoft.com/en-gb/entra/architecture/security-operations-user-accounts#monitoring-external-user-sign-ins
author: <PERSON><PERSON><PERSON><PERSON><PERSON>, '@dudders1'
date: 2022-06-30
tags:
    - attack.privilege-escalation
    - attack.initial-access
    - attack.t1078.004
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        Category: 'UserManagement'
        OperationName: 'Update user'
        properties.message: '"displayName":"UserType","oldValue":"[\"Guest\"]","newValue":"[\"Member\"]"'
    condition: selection
falsepositives:
    - If this was approved by System Administrator.
level: medium
