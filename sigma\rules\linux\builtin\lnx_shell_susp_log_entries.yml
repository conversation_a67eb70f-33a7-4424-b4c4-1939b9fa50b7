title: Suspicious Log Entries
id: f64b6e9a-5d9d-48a5-8289-e1dd2b3876e1
status: test
description: Detects suspicious log entries in Linux log files
references:
    - https://github.com/ossec/ossec-hids/blob/f6502012b7380208db81f82311ad4a1994d39905/etc/rules/syslog_rules.xml
author: <PERSON><PERSON><PERSON> (Nextron Systems)
date: 2017-03-25
modified: 2021-11-27
tags:
    - attack.impact
logsource:
    product: linux
detection:
    keywords:
        # Generic suspicious log lines
        - 'entered promiscuous mode'
        # OSSEC https://github.com/ossec/ossec-hids/blob/master/etc/rules/syslog_rules.xml
        - 'Deactivating service'
        - 'Oversized packet received from'
        - 'imuxsock begins to drop messages'
    condition: keywords
falsepositives:
    - Unknown
level: medium
