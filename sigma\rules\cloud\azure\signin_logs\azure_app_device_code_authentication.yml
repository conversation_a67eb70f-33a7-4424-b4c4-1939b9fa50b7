title: Application Using Device Code Authentication Flow
id: 248649b7-d64f-46f0-9fb2-a52774166fb5
status: test
description: |
    Device code flow is an OAuth 2.0 protocol flow specifically for input constrained devices and is not used in all environments.
    If this type of flow is seen in the environment and not being used in an input constrained device scenario, further investigation is warranted.
    This can be a misconfigured application or potentially something malicious.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#application-authentication-flows
author: <PERSON> '@markmorow', <PERSON> '@baileybercik'
date: 2022-06-01
tags:
    - attack.t1078
    - attack.defense-evasion
    - attack.persistence
    - attack.privilege-escalation
    - attack.initial-access
logsource:
    product: azure
    service: signinlogs
detection:
    selection:
        properties.message: Device Code
    condition: selection
falsepositives:
    - Applications that are input constrained will need to use device code flow and are valid authentications.
level: medium
