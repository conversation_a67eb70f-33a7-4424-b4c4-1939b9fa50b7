title: Bitbucket Project Secret Scanning Allowlist Added
id: 42ccce6d-7bd3-4930-95cd-e4d83fa94a30
status: test
description: Detects when a secret scanning allowlist rule is added for projects.
references:
    - https://confluence.atlassian.com/bitbucketserver/audit-log-events-776640423.html
    - https://confluence.atlassian.com/bitbucketserver/secret-scanning-1157471613.html
author: <PERSON> (@faisalusuf)
date: 2024-02-25
tags:
    - attack.defense-evasion
    - attack.t1562.001
logsource:
    product: bitbucket
    service: audit
    definition: 'Requirements: "Basic" log level is required to receive these audit events.'
detection:
    selection:
        auditType.category: 'Projects'
        auditType.action: 'Project secret scanning allowlist rule added'
    condition: selection
falsepositives:
    - Legitimate user activity.
level: low
