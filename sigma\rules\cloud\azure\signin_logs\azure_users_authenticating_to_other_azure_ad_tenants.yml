title: Users Authenticating To Other Azure AD Tenants
id: 5f521e4b-0105-4b72-845b-2198a54487b9
status: test
description: Detect when users in your Azure AD tenant are authenticating to other Azure AD Tenants.
references:
    - https://learn.microsoft.com/en-gb/entra/architecture/security-operations-user-accounts#monitoring-external-user-sign-ins
author: <PERSON><PERSON><PERSON><PERSON><PERSON>, '@dudders1'
date: 2022-06-30
tags:
    - attack.initial-access
    - attack.t1078.004
logsource:
    product: azure
    service: signinlogs
detection:
    selection:
        Status: 'Success'
        HomeTenantId: 'HomeTenantID'
    filter:
        ResourceTenantId|contains: 'HomeTenantID'
    condition: selection and not filter
falsepositives:
    - If this was approved by System Administrator.
level: medium
