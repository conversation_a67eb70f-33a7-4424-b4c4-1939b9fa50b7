title: Suspicious SQL Error Messages
id: 8a670c6d-7189-4b1c-8017-a417ca84a086
status: test
description: Detects SQL error messages that indicate probing for an injection attack
references:
    - http://www.sqlinjection.net/errors
author: B<PERSON><PERSON>
date: 2017-11-27
modified: 2023-02-12
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: sql
    definition: 'Requirements: application error logs must be collected (with LOG_LEVEL ERROR and above)'
detection:
    keywords:
        # Oracle
        - quoted string not properly terminated
        # MySQL
        - You have an error in your SQL syntax
        # SQL Server
        - Unclosed quotation mark
        # SQLite
        - 'near "*": syntax error'
        - SELECTs to the left and right of UNION do not have the same number of result columns
    condition: keywords
falsepositives:
    - A syntax error in MySQL also occurs in non-dynamic (safe) queries if there is an empty in() clause, that may often be the case.
level: high
