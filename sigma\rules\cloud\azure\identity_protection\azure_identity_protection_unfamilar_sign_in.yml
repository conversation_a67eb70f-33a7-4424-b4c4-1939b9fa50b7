title: Unfamiliar Sign-In Properties
id: 128faeef-79dd-44ca-b43c-a9e236a60f49
status: test
description: Detects sign-in with properties that are unfamiliar to the user. The detection considers past sign-in history to look for anomalous sign-ins.
references:
    - https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#unfamiliar-sign-in-properties
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-user-accounts#unusual-sign-ins
author: <PERSON> '@markmorow', <PERSON>, '@gleeiamglo'
date: 2023-09-03
tags:
    - attack.t1078
    - attack.persistence
    - attack.defense-evasion
    - attack.privilege-escalation
    - attack.initial-access
logsource:
    product: azure
    service: riskdetection
detection:
    selection:
        riskEventType: 'unfamiliarFeatures'
    condition: selection
falsepositives:
    - User changing to a new device, location, browser, etc.
level: high
