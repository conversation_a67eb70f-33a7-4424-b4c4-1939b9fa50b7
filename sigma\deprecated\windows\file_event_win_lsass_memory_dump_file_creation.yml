title: LSASS Memory Dump File Creation
id: 5e3d3601-0662-4af0-b1d2-36a05e90c40a
status: deprecated
description: LSASS memory dump creation using operating systems utilities. Procdump will use process name in output file if no name is specified
references:
    - https://www.slideshare.net/heirhabarov/hunting-for-credentials-dumping-in-windows-environment
author: <PERSON><PERSON><PERSON>, oscd.community
date: 2019/10/22
modified: 2023/08/29
tags:
    - attack.credential_access
    - attack.t1003.001
logsource:
    category: file_event
    product: windows
detection:
    selection:
        TargetFilename|contains: 'lsass'
        TargetFilename|endswith: 'dmp'
    condition: selection
fields:
    - ComputerName
    - TargetFilename
falsepositives:
    - Dumping lsass memory for forensic investigation purposes by legitimate incident responder or forensic invetigator
    - Dumps of another process that contains lsass in its process name (substring)
level: high
