title: <PERSON><PERSON> FastPass Phishing Detection
id: ee39a9f7-5a79-4b0a-9815-d36b3cf28d3e
status: test
description: Detects when Okta FastPass prevents a known phishing site.
references:
    - https://sec.okta.com/fastpassphishingdetection
    - https://developer.okta.com/docs/reference/api/system-log/
    - https://developer.okta.com/docs/reference/api/event-types/
author: <PERSON> @austinsonger
date: 2023-05-07
tags:
    - attack.initial-access
    - attack.t1566
logsource:
    product: okta
    service: okta
detection:
    selection:
        outcome.reason: 'FastPass declined phishing attempt'
        outcome.result: FAILURE
        eventtype: user.authentication.auth_via_mfa
    condition: selection
falsepositives:
    - Unlikely
level: high
