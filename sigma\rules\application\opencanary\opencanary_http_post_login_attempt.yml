title: OpenCanary - HTTP POST Login Attempt
id: af1ac430-df6b-4b38-b976-0b52f07a0252
status: test
description: |
    Detects instances where an HTTP service on an OpenCanary node has had login attempt via Form POST.
references:
    - https://opencanary.readthedocs.io/en/latest/starting/configuration.html#services-configuration
    - https://github.com/thinkst/opencanary/blob/a0896adfcaf0328cfd5829fe10d2878c7445138e/opencanary/logger.py#L52
author: Security Onion Solutions
date: 2024-03-08
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: opencanary
detection:
    selection:
        logtype: 3001
    condition: selection
falsepositives:
    - Unlikely
level: high
