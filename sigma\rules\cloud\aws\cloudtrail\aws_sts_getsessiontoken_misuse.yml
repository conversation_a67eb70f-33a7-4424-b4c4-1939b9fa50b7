title: AWS STS GetSessionToken Misuse
id: b45ab1d2-712f-4f01-a751-df3826969807
status: test
description: Identifies the suspicious use of GetSessionToken. Tokens could be created and used by attackers to move laterally and escalate privileges.
references:
    - https://github.com/elastic/detection-rules/pull/1213
    - https://docs.aws.amazon.com/STS/latest/APIReference/API_GetSessionToken.html
author: <PERSON> @austinsonger
date: 2021-07-24
modified: 2022-10-09
tags:
    - attack.lateral-movement
    - attack.privilege-escalation
    - attack.t1548
    - attack.t1550
    - attack.t1550.001
logsource:
    product: aws
    service: cloudtrail
detection:
    selection:
        eventSource: sts.amazonaws.com
        eventName: GetSessionToken
        userIdentity.type: IAMUser
    condition: selection
falsepositives:
    - GetSessionToken may be done by a system or network administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment. GetSessionToken from unfamiliar users or hosts should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: low
