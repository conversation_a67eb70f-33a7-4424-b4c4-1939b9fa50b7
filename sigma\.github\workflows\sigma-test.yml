# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Sigma Rule Tests

on:
  push:
    branches:
      - "*"
    paths:
      - ".github/workflows/sigma-test.yml"
      - "deprecated/**.yml"
      - "rules-compliance/**.yml"
      - "rules-dfir/**.yml"
      - "rules-emerging-threats/**.yml"
      - "rules-placeholder/**.yml"
      - "rules-threat-hunting/**.yml"
      - "rules/**.yml"
      - "tests/sigma_cli_conf.yml"
      - "tests/test_logsource.py"
      - "tests/test_rules.py"
      - "unsupported/**.yml"
  pull_request:
    branches:
      - master
    paths:
      - ".github/workflows/sigma-test.yml"
      - "deprecated/**.yml"
      - "rules-compliance/**.yml"
      - "rules-dfir/**.yml"
      - "rules-emerging-threats/**.yml"
      - "rules-placeholder/**.yml"
      - "rules-threat-hunting/**.yml"
      - "rules/**.yml"
      - "tests/sigma_cli_conf.yml"
      - "tests/test_logsource.py"
      - "tests/test_rules.py"
      - "unsupported/**.yml"

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  yamllint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: yaml-lint
      uses: ibiqlik/action-yamllint@v3

  test-sigma-logsource:
    runs-on: ubuntu-latest
    needs: yamllint
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
    - name: Set up Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: 3.11
    - name: Test Sigma logsource
      run: |
        pip install PyYAML colorama
        python tests/test_logsource.py

  test-sigma:
    runs-on: ubuntu-latest
    needs: test-sigma-logsource
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
    - name: Set up Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: 3.11
    - name: Install dependencies
      run: |
        pip install pysigma
        pip install sigma-cli
        pip install pySigma-validators-sigmahq==0.9.*
    - name: Test Sigma Rule Syntax
      run: |
        sigma check --fail-on-error --fail-on-issues --validation-config tests/sigma_cli_conf.yml rules*
    - name: Test Sigma Rules
      run: |
        pip install PyYAML colorama
        python tests/test_rules.py
