name: Auto message for <PERSON>'s and Issues

on: [pull_request_target, issues]

jobs:
  build:
    name: Hello new contributor
    runs-on: ubuntu-latest
    steps:
      - uses: actions/first-interaction@v1.3.0
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          issue-message: |-
            Welcome @${{github.actor}} :wave:

            It looks like this is your first issue on the Sigma rules repository!

            The following repository accepts issues related to `false positives` or 'rule ideas'.

            If you're reporting an issue related to the pySigma library please consider submitting it [here](https://github.com/SigmaHQ/pySigma)

            If you're reporting an issue related to the deprecated sigmac library please consider submitting it [here](https://github.com/SigmaHQ/legacy-sigmatools)

            Thanks for taking the time to open this issue, and welcome to the Sigma community! :smiley:


          pr-message: |-
            Welcome @${{github.actor}} :wave:
            
            It looks like this is your first pull request on the Sigma rules repository!

            Please make sure to read the [SigmaHQ conventions](https://github.com/SigmaHQ/sigma-specification/blob/main/sigmahq/) document to make sure your contribution is adhering to best practices and has all the necessary elements in place for a successful approval.

            Thanks again, and welcome to the Sigma community! :smiley:
