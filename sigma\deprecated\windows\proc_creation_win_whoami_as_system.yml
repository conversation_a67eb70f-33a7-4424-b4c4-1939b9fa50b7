title: Run Whoami as SYSTEM
id: 80167ada-7a12-41ed-b8e9-aa47195c66a1
status: deprecated
description: Detects a whoami.exe executed by LOCAL SYSTEM. This may be a sign of a successful local privilege escalation.
references:
    - https://speakerdeck.com/heirhabarov/hunting-for-privilege-escalation-in-windows-environment
author: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
date: 2019/10/23
modified: 2023/02/28
tags:
    - attack.privilege_escalation
    - attack.discovery
    - attack.t1033
logsource:
    category: process_creation
    product: windows
detection:
    selection_user:
        User|contains: # covers many language settings
            - 'AUTHORI'
            - 'AUTORI'
    selection_img:
        - OriginalFileName: 'whoami.exe'
        - Image|endswith: '\whoami.exe'
    condition: all of selection*
falsepositives:
    - Possible name overlap with NT AUHTORITY substring to cover all languages
level: high
