title: Google Cloud Storage Buckets Modified or Deleted
id: 4d9f2ee2-c903-48ab-b9c1-8c0f474913d0
status: test
description: Detects when storage bucket is modified or deleted in Google Cloud.
references:
    - https://cloud.google.com/storage/docs/json_api/v1/buckets
author: <PERSON> @austinsonger
date: 2021-08-14
modified: 2022-10-09
tags:
    - attack.impact
logsource:
    product: gcp
    service: gcp.audit
detection:
    selection:
        gcp.audit.method_name:
            - storage.buckets.delete
            - storage.buckets.insert
            - storage.buckets.update
            - storage.buckets.patch
    condition: selection
falsepositives:
    - Storage Buckets being modified or deleted may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Storage Buckets modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
