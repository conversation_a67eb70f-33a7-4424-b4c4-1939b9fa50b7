title: Github New Secret Created
id: f9405037-bc97-4eb7-baba-167dad399b83
status: test
description: Detects when a user creates action secret for the organization, environment, codespaces or repository.
author: <PERSON> (@faisalusuf)
date: 2023-01-20
references:
    - https://docs.github.com/en/organizations/keeping-your-organization-secure/managing-security-settings-for-your-organization/reviewing-the-audit-log-for-your-organization#audit-log-actions
tags:
    - attack.defense-evasion
    - attack.persistence
    - attack.privilege-escalation
    - attack.initial-access
    - attack.t1078.004
logsource:
    product: github
    service: audit
    definition: 'Requirements: The audit log streaming feature must be enabled to be able to receive such logs. You can enable following the documentation here: https://docs.github.com/en/enterprise-cloud@latest/admin/monitoring-activity-in-your-enterprise/reviewing-audit-logs-for-your-enterprise/streaming-the-audit-log-for-your-enterprise#setting-up-audit-log-streaming'
detection:
    selection:
        action:
            - 'codespaces.create_an_org_secret'
            - 'environment.create_actions_secret'
            - 'org.create_actions_secret'
            - 'repo.create_actions_secret'
    condition: selection
falsepositives:
    - This detection cloud be noisy depending on the environment. It is recommended to keep a check on the new secrets when created and validate the "actor".
level: low
