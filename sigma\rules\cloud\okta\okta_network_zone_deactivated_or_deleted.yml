title: Okta Network Zone Deactivated or Deleted
id: 9f308120-69ed-4506-abde-ac6da81f4310
status: test
description: Detects when an Network Zone is Deactivated or Deleted.
references:
    - https://developer.okta.com/docs/reference/api/system-log/
    - https://developer.okta.com/docs/reference/api/event-types/
author: <PERSON> @austinsonger
date: 2021-09-12
modified: 2022-10-09
tags:
    - attack.impact
logsource:
    product: okta
    service: okta
detection:
    selection:
        eventtype:
            - zone.deactivate
            - zone.delete
    condition: selection
falsepositives:
    - Unknown

level: medium
