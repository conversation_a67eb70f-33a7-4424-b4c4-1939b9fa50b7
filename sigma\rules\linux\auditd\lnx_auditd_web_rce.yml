title: Webshell Remote Command Execution
id: c0d3734d-330f-4a03-aae2-65dacc6a8222
status: test
description: Detects possible command execution by web application/web shell
references:
    - Personal Experience of the Author
    - https://www.vaadata.com/blog/what-is-command-injection-exploitations-and-security-best-practices/
author: <PERSON><PERSON>, <PERSON><PERSON>, oscd.community
date: 2019-10-12
modified: 2025-06-03
tags:
    - attack.persistence
    - attack.t1505.003
logsource:
    product: linux
    service: auditd
    definition: |
        Required auditd configuration:
        -a always,exit -F arch=b32 -S execve -F euid=33 -k detect_execve_www
        -a always,exit -F arch=b64 -S execve -F euid=33 -k detect_execve_www
        -a always,exit -F arch=b32 -S execveat -F euid=33 -k detect_execve_www
        -a always,exit -F arch=b64 -S execveat -F euid=33 -k detect_execve_www
        Change the number "33" to the ID of your WebServer user. Default: www-data:x:33:33
detection:
    selection:
        type: 'SYSCALL'
        syscall:
            - 'execve'
            - 'execveat'
        euid: 33
    condition: selection
falsepositives:
    - Admin activity
    - Crazy web applications
level: critical
