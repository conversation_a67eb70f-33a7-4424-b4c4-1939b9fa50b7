title: New CA Policy by Non-approved Actor
id: 0922467f-db53-4348-b7bf-dee8d0d348c6
status: test
description: Monitor and alert on conditional access changes.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-infrastructure
author: <PERSON><PERSON>, '@corissalea'
date: 2022-07-18
tags:
    - attack.defense-evasion
    - attack.t1548
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message: Add conditional access policy
    condition: selection
falsepositives:
    - Misconfigured role permissions
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
level: medium
