title: WMI Reconnaissance List Remote Services
id: 09af397b-c5eb-4811-b2bb-08b3de464ebf
status: deprecated
description: |
    An adversary might use WMI to check if a certain Remote Service is running on a remote device.
    When the test completes, a service information will be displayed on the screen if it exists.
    A common feedback message is that "No instance(s) Available" if the service queried is not running.
    A common error message is "Node - (provided IP or default) ERROR Description =The RPC server is unavailable" if the provided remote host is unreachable
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1047/T1047.md
    - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/wmic
author: frack113
date: 2022/01/01
modified: 2023/02/14
tags:
    - attack.execution
    - attack.t1047
logsource:
    category: process_creation
    product: windows
detection:
    selection_img:
        - Image|endswith: '\WMIC.exe'
        - OriginalFileName: 'wmic.exe'
    selection_cli:
        CommandLine|contains|all:
            - '/node:'
            - 'service'
    condition: all of selection*
falsepositives:
    - Unknown
level: medium
