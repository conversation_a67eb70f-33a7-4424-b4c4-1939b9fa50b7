title: Roles Activation Doesn't Require MFA
id: 94a66f46-5b64-46ce-80b2-75dcbe627cc0
status: test
description: Identifies when a privilege role can be activated without performing mfa.
references:
    - https://learn.microsoft.com/en-us/entra/id-governance/privileged-identity-management/pim-how-to-configure-security-alerts#roles-dont-require-multi-factor-authentication-for-activation
author: <PERSON> '@markmorow', <PERSON>, '@gleeiamglo'
date: 2023-09-14
tags:
    - attack.t1078
    - attack.persistence
    - attack.privilege-escalation
logsource:
    product: azure
    service: pim
detection:
    selection:
        riskEventType: 'noMfaOnRoleActivationAlertIncident'
    condition: selection
falsepositives:
    - Investigate if user is performing MFA at sign-in.
level: high
