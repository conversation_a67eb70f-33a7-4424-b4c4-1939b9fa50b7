title: Delete Volume Shadow Copies via WMI with PowerShell - PS Script
id: e17121b4-ef2a-4418-8a59-12fb1631fa9e
related:
    - id: 21ff4ca9-f13a-41ad-b828-0077b2af2e40
      type: similar
    - id: c1337eb8-921a-4b59-855b-4ba188ddcc42
      type: similar
status: deprecated
description: Deletes Windows Volume Shadow Copies with PowerShell code and Get-WMIObject. This technique is used by numerous ransomware families such as Sodinokibi/REvil
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1490/T1490.md#atomic-test-5---windows---delete-volume-shadow-copies-via-wmi-with-powershell
author: frack113
date: 2021-12-26
modified: 2025-05-20
tags:
    - attack.impact
    - attack.t1490
logsource:
    product: windows
    category: ps_script
    definition: 'Requirements: Script Block Logging must be enabled'
detection:
    selection:
        ScriptBlockText|contains|all:
            - 'Get-WmiObject'
            - 'Win32_ShadowCopy'
            - '.Delete()'
    condition: selection
falsepositives:
    - Unknown
level: high
