title: Windows LAPS Credential Dump From Entra ID
id: a4b25073-8947-489c-a8dd-93b41c23f26d
status: test
description: Detects when an account dumps the LAPS password from Entra ID.
references:
    - https://twitter.com/NathanMcNulty/status/1785051227568632263
    - https://www.cloudcoffee.ch/microsoft-365/configure-windows-laps-in-microsoft-intune/
    - https://techcommunity.microsoft.com/t5/microsoft-entra-blog/introducing-windows-local-administrator-password-solution-with/ba-p/1942487
author: andrewdanis
date: 2024-06-26
tags:
    - attack.privilege-escalation
    - attack.persistence
    - attack.t1098.005
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        category: 'Device'
        activityType|contains: 'Recover device local administrator password'
        additionalDetails.additionalInfo|contains: 'Successfully recovered local credential by device id'
    condition: selection
falsepositives:
    - Approved activity performed by an Administrator.
level: high
