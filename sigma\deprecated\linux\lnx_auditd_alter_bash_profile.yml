title: Edit of .bash_profile and .bashrc
id: e74e15cc-c4b6-4c80-b7eb-dfe49feb7fe9
status: deprecated
description: Detects change of user environment. Adversaries can insert code into these files to gain persistence each time a user logs in or opens a new shell.
references:
    - 'MITRE Attack technique T1156; .bash_profile and .bashrc. '
author: <PERSON>
date: 2019/05/12
modified: 2023/03/23
tags:
    - attack.s0003
    - attack.persistence
    - attack.t1546.004
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'PATH'
        name:
            - '/root/.bashrc'
            - '/root/.bash_profile'
            - '/root/.profile'
            - '/home/<USER>/.bashrc'
            - '/home/<USER>/.bash_profile'
            - '/home/<USER>/.profile'
            - '/etc/profile'
            - '/etc/shells'
            - '/etc/bashrc'
            - '/etc/csh.cshrc'
            - '/etc/csh.login'
    condition: selection
falsepositives:
    - Admin or User activity
level: medium
