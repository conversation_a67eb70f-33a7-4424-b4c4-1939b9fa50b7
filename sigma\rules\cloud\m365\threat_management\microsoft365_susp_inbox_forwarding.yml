title: Suspicious Inbox Forwarding
id: 6c220477-0b5b-4b25-bb90-66183b4089e8
status: test
description: Detects when a Microsoft Cloud App Security reported suspicious email forwarding rules, for example, if a user created an inbox rule that forwards a copy of all emails to an external address.
references:
    - https://learn.microsoft.com/en-us/defender-cloud-apps/anomaly-detection-policy
    - https://learn.microsoft.com/en-us/defender-cloud-apps/policy-template-reference
author: <PERSON> @austinsonger
date: 2021-08-22
modified: 2022-10-09
tags:
    - attack.exfiltration
    - attack.t1020
logsource:
    service: threat_management
    product: m365
detection:
    selection:
        eventSource: SecurityComplianceCenter
        eventName: 'Suspicious inbox forwarding'
        status: success
    condition: selection
falsepositives:
    - Unknown
level: low
