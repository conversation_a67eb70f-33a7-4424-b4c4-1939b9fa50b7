title: Suspicious In-Memory Module Execution
id: 5f113a8f-8b61-41ca-b90f-d374fa7e4a39
status: deprecated
description: |
    Detects the access to processes by other suspicious processes which have reflectively loaded libraries in their memory space.
    An example is SilentTrinity C2 behaviour. Generally speaking, when Sysmon EventID 10 cannot reference a stack call to a dll loaded from disk (the standard way),
    it will display "UNKNOWN" as the module name. Usually this means the stack call points to a module that was reflectively loaded in memory.
    Adding to this, it is not common to see such few calls in the stack (ntdll.dll --> kernelbase.dll --> unknown) which essentially means that
    most of the functions required by the process to execute certain routines are already present in memory, not requiring any calls to external libraries.
    The latter should also be considered suspicious.
references:
    - https://azure.microsoft.com/en-ca/blog/detecting-in-memory-attacks-with-sysmon-and-azure-security-center/
author: <PERSON> (@darkquassar), oscd.community, Jonhnath<PERSON>
date: 2019/10/27
modified: 2022/11/17
tags:
    - attack.privilege_escalation
    - attack.defense_evasion
    - attack.t1055.001
    - attack.t1055.002
logsource:
    category: process_access
    product: windows
detection:
    selection1:
        CallTrace|contains|all:
            - 'C:\WINDOWS\SYSTEM32\ntdll.dll+'
            - '|C:\WINDOWS\System32\KERNELBASE.dll+'
            - '|UNKNOWN('
            - ')'
    selection2:
        CallTrace|contains|all:
            - 'UNKNOWN('
            - ')|UNKNOWN('
        CallTrace|endswith: ')'
    selection3:
        CallTrace|contains: 'UNKNOWN'
        GrantedAccess:
            - '0x1F0FFF'
            - '0x1F1FFF'
            - '0x143A'
            - '0x1410'
            - '0x1010'
            - '0x1F2FFF'
            - '0x1F3FFF'
            - '0x1FFFFF'
    filter:
        - SourceImage|endswith:
            - '\Windows\System32\sdiagnhost.exe'
            - '\procexp64.exe'
            - '\procexp.exe'
            - '\Microsoft VS Code\Code.exe'
            - '\aurora-agent-64.exe'
            - '\aurora-agent.exe'
            - '\git\usr\bin\sh.exe'
            - '\IDE\devenv.exe'
            - '\GitHubDesktop\Update.exe'
            - '\RuntimeBroker.exe'
            - '\backgroundTaskHost.exe'
            - '\GitHubDesktop.exe'
        - SourceImage|startswith:
            - 'C:\Program Files (x86)\'
            - 'C:\Program Files\'
            - 'C:\Windows\Microsoft.NET\Framework\\*\NGenTask.exe'
            - 'C:\Program Files (x86)\Microsoft Visual Studio\'
            - 'C:\Program Files\Microsoft Visual Studio\'
            - 'C:\Windows\Microsoft.NET\Framework'
            - 'C:\WINDOWS\System32\DriverStore\'
            - 'C:\Windows\System32\WindowsPowerShell\'
        - SourceImage:
            - 'C:\WINDOWS\system32\taskhostw.exe'
            - 'C:\WINDOWS\system32\ctfmon.exe'
            - 'C:\WINDOWS\system32\NhNotifSys.exe'
            - 'C:\Windows\ImmersiveControlPanel\SystemSettings.exe'
            - 'C:\Windows\explorer.exe'
        - TargetImage: 'C:\Windows\System32\RuntimeBroker.exe'
        - TargetImage|endswith: '\Microsoft VS Code\Code.exe'
        - CallTrace|contains: '|C:\WINDOWS\System32\RPCRT4.dll+'  # attempt to save the rule with a broader filter
    filter_set_1:
        SourceImage: 'C:\WINDOWS\Explorer.EXE'
        TargetImage:
            - 'C:\WINDOWS\system32\backgroundTaskHost.exe'
            - 'C:\WINDOWS\explorer.exe'
    filter_msmpeng:
        SourceImage|startswith: 'C:\ProgramData\Microsoft\Windows Defender\Platform\'
        SourceImage|endswith: '\MsMpEng.exe'
    filter_eclipse:
        SourceImage|endswith: '\eclipse.exe'
        CallTrace|contains:
            - '\jre\bin\java.dll'
            - '|C:\Windows\SYSTEM32\windows.storage.dll+'
            - '\configuration\org.eclipse.osgi\'
    filter_openwith:
        SourceImage: 'C:\Windows\system32\OpenWith.exe'
        TargetImage: 'C:\Windows\Explorer.EXE'
    filter_teams:
        SourceImage|startswith: 'C:\Users\<USER>\AppData\Local\Microsoft\Teams\current\Teams.exe'
        TargetImage|endswith:
            - ':\Windows\Explorer.EXE'
            - '\AppData\Local\Microsoft\Teams\Update.exe'
            - '\AppData\Local\Microsoft\Teams\current\Teams.exe'
            - '\MsMpEng.exe'
    filter_wwahost:
        SourceImage: 'C:\Windows\System32\WWAHost.exe'
        TargetImage: 'C:\Windows\System32\svchost.exe'
    filter_sppsvc:
        SourceImage: C:\WINDOWS\system32\sppsvc.exe
        TargetImage: C:\WINDOWS\system32\SppExtComObj.exe
    condition: 1 of selection* and not 1 of filter*
fields:
    - ComputerName
    - User
    - SourceImage
    - TargetImage
    - CallTrace
falsepositives:
    - SysInternals Process Explorer
level: low
