title: Bitbucket Global Permission Changed
id: aac6c4f4-87c7-4961-96ac-c3fd3a42c310
status: test
description: Detects global permissions change activity.
references:
    - https://confluence.atlassian.com/bitbucketserver/audit-log-events-776640423.html
    - https://confluence.atlassian.com/bitbucketserver/global-permissions-776640369.html
author: <PERSON> (@faisalusuf)
date: 2024-02-25
tags:
    - attack.persistence
    - attack.privilege-escalation
    - attack.t1098
logsource:
    product: bitbucket
    service: audit
    definition: 'Requirements: "Advance" log level is required to receive these audit events.'
detection:
    selection:
        auditType.category: 'Permissions'
        auditType.action:
            - 'Global permission remove request'
            - 'Global permission removed'
            - 'Global permission granted'
            - 'Global permission requested'
    condition: selection
falsepositives:
    - Legitimate user activity.
level: medium
