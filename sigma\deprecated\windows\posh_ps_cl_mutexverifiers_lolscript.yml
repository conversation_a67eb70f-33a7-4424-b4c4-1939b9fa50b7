title: Execution via CL_Mutexverifiers.ps1
id: 39776c99-1c7b-4ba0-b5aa-641525eee1a4
status: deprecated
description: Detects Execution via runAfterCancelProcess in CL_Mutexverifiers.ps1 module
references:
    - https://lolbas-project.github.io/lolbas/Scripts/CL_mutexverifiers/
    - https://twitter.com/pabraeken/status/995111125447577600
author: oscd.community, <PERSON>
date: 2020/10/14
modified: 2023/08/17
tags:
    - attack.defense_evasion
    - attack.t1216
logsource:
    product: windows
    category: ps_script
    definition: 'Requirements: Script Block Logging must be enabled'
detection:
    selection:
        ScriptBlockText|contains|all:
            - 'CL_Mutexverifiers.ps1'
            - 'runAfterCancelProcess'
    condition: selection
falsepositives:
    - Unknown
level: high
