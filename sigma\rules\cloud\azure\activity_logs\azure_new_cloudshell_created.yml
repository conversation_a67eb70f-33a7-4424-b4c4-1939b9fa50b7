title: Azure New CloudShell Created
id: 72af37e2-ec32-47dc-992b-bc288a2708cb
status: test
description: Identifies when a new cloudshell is created inside of Azure portal.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON>
date: 2021-09-21
modified: 2022-08-23
tags:
    - attack.execution
    - attack.t1059
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName: MICROSOFT.PORTAL/CONSOLES/WRITE
    condition: selection
falsepositives:
    - A new cloudshell may be created by a system administrator.
level: medium
