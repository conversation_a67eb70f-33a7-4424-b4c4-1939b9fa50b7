title: Google Workspace Granted Domain API Access
id: 04e2a23a-9b29-4a5c-be3a-3542e3f982ba
status: test
description: Detects when an API access service account is granted domain authority.
references:
    - https://cloud.google.com/logging/docs/audit/gsuite-audit-logging#3
    - https://developers.google.com/admin-sdk/reports/v1/appendix/activity/admin-domain-settings#AUTHORIZE_API_CLIENT_ACCESS
author: <PERSON>
date: 2021-08-23
modified: 2023-10-11
tags:
    - attack.persistence
    - attack.t1098
logsource:
    product: gcp
    service: google_workspace.admin
detection:
    selection:
        eventService: admin.googleapis.com
        eventName: AUTHORIZE_API_CLIENT_ACCESS
    condition: selection
falsepositives:
    - Unknown

level: medium
