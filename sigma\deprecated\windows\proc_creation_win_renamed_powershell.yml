title: Renamed PowerShell
id: d178a2d7-129a-4ba4-8ee6-d6e1fecd5d20
status: deprecated
description: Detects the execution of a renamed PowerShell often used by attackers or malware
references:
    - https://twitter.com/christophetd/status/1164506034720952320
author: <PERSON><PERSON><PERSON> (Nextron Systems), frack113
date: 2019/08/22
modified: 2023/01/18
tags:
    - car.2013-05-009
    - attack.defense_evasion
    - attack.t1036.003
logsource:
    product: windows
    category: process_creation
detection:
    selection:
        Description|startswith:
            - 'Windows PowerShell'
            - 'pwsh'
        Company: 'Microsoft Corporation'
    filter:
        Image|endswith:
            - '\powershell.exe'
            - '\powershell_ise.exe'
            - '\pwsh.exe'
    condition: selection and not filter
falsepositives:
    - Unknown
level: high
