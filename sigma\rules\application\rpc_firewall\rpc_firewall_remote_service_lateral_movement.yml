title: Remote Server Service Abuse for Lateral Movement
id: 10018e73-06ec-46ec-8107-9172f1e04ff2
status: test
description: Detects remote RPC calls to possibly abuse remote encryption service via MS-EFSR
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-srvs/accf23b0-0f57-441c-9185-43041f1b0ee9
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-SCMR.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, De<PERSON>
date: 2022-01-01
tags:
    - attack.lateral-movement
    - attack.execution
    - attack.t1569.002
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:367abb81-9844-35f1-ad32-98f038001003'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 367abb81-9844-35f1-ad32-98f038001003
    condition: selection
falsepositives:
    - Administrative tasks on remote services
level: high
