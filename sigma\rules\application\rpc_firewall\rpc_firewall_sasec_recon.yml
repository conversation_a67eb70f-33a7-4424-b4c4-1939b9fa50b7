title: Recon Activity via SASec
id: 0a3ff354-93fc-4273-8a03-1078782de5b7
status: test
description: Detects remote RPC calls to read information about scheduled tasks via SASec
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-tsch/d1058a28-7e02-4948-8b8d-4a347fa64931
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-TSCH.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, De<PERSON>
date: 2022-01-01
tags:
    - attack.discovery
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:378e52b0-c0a9-11cf-822d-00aa0051e40f"'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 378e52b0-c0a9-11cf-822d-00aa0051e40f
    filter:
        OpNum:
            - 0
            - 1
    condition: selection and not filter
falsepositives:
    - Unknown
level: high
