title: Azure Keyvault Secrets Modified or Deleted
id: b831353c-1971-477b-abb6-2828edc3bca1
status: test
description: Identifies when secrets are modified or deleted in Azure.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON> @austinsonger
date: 2021-08-16
modified: 2022-08-23
tags:
    - attack.impact
    - attack.credential-access
    - attack.t1552
    - attack.t1552.001
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.KEYVAULT/VAULTS/SECRETS/WRITE
            - MICROSOFT.KEYVAULT/VAULTS/SECRETS/DELETE
            - MICROSOFT.KEYVAULT/VAULTS/SECRETS/BACKUP/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/SECRETS/PURGE/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/SECRETS/UPDATE/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/SECRETS/RECOVER/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/SECRETS/RESTORE/ACTION
            - MICROSOFT.KEYVAULT/VAULTS/SECRETS/SETSECRET/ACTION
    condition: selection
falsepositives:
    - Secrets being modified or deleted may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Secrets modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
