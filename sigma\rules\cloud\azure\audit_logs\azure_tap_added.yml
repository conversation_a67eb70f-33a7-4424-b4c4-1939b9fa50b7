title: Temporary Access Pass Added To An Account
id: fa84aaf5-8142-43cd-9ec2-78cfebf878ce
status: test
description: Detects when a temporary access pass (TAP) is added to an account. TAPs added to priv accounts should be investigated
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts#changes-to-privileged-accounts
author: <PERSON> '@markmorow', <PERSON><PERSON><PERSON>, '@Yochana-H'
date: 2022-08-10
tags:
    - attack.persistence
    - attack.t1078.004
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message: Admin registered security info
        Status: Admin registered temporary access pass method for user
    condition: selection
falsepositives:
    - Administrator adding a legitimate temporary access pass
level: high
