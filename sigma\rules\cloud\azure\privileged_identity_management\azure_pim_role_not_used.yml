title: Roles Are Not Being Used
id: 8c6ec464-4ae4-43ac-936a-291da66ed13d
status: test
description: Identifies when a user has been assigned a privilege role and are not using that role.
references:
    - https://learn.microsoft.com/en-us/entra/id-governance/privileged-identity-management/pim-how-to-configure-security-alerts#administrators-arent-using-their-privileged-roles
author: <PERSON> '@markmorow', <PERSON>, '@gleeiamglo'
date: 2023-09-14
tags:
    - attack.t1078
    - attack.persistence
    - attack.privilege-escalation
logsource:
    product: azure
    service: pim
detection:
    selection:
        riskEventType: 'redundantAssignmentAlertIncident'
    condition: selection
falsepositives:
    - Investigate if potential generic account that cannot be removed.
level: high
