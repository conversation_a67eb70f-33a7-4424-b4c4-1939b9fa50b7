title: Bitbucket Audit Log Configuration Updated
id: 6aa12161-235a-4dfb-9c74-fe08df8d8da1
status: test
description: Detects changes to the bitbucket audit log configuration.
references:
    - https://confluence.atlassian.com/bitbucketserver/view-and-configure-the-audit-log-776640417.html
author: <PERSON> (@faisalusuf)
date: 2024-02-25
tags:
    - attack.defense-evasion
    - attack.t1562.001
logsource:
    product: bitbucket
    service: audit
    definition: 'Requirements: "Basic" log level is required to receive these audit events.'
detection:
    selection:
        auditType.category: 'Auditing'
        auditType.action: 'Audit log configuration updated'
    condition: selection
falsepositives:
    - Legitimate user activity.
level: medium
