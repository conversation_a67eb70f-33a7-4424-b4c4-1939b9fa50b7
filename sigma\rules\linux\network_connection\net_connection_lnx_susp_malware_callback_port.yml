title: Potentially Suspicious Malware Callback Communication - Linux
id: dbfc7c98-04ab-4ab7-aa94-c74d22aa7376
related:
    - id: 4b89abaa-99fe-4232-afdd-8f9aa4d20382
      type: derived
status: test
description: |
    Detects programs that connect to known malware callback ports based on threat intelligence reports.
references:
    - https://www.mandiant.com/resources/blog/triton-actor-ttp-profile-custom-attack-tools-detections
    - https://www.mandiant.com/resources/blog/ukraine-and-sandworm-team
    - https://www.elastic.co/guide/en/security/current/potential-non-standard-port-ssh-connection.html
    - https://thehackernews.com/2024/01/systembc-malwares-c2-server-analysis.html
    - https://www.cybereason.com/blog/sliver-c2-leveraged-by-many-threat-actors
author: hasselj
date: 2024-05-10
tags:
    - attack.persistence
    - attack.command-and-control
    - attack.t1571
logsource:
    category: network_connection
    product: linux
detection:
    selection:
        Initiated: 'true'
        DestinationPort:
            - 888
            - 999
            - 2200
            - 2222
            - 4000
            - 4444
            - 6789
            - 8531
            - 50501
            - 51820
    filter_main_local_ranges:
        DestinationIp|cidr:
            - '*********/8'
            - '10.0.0.0/8'
            - '**********/12'
            - '***********/16'
            - '***********/16'
            - '::1/128'         # IPv6 loopback
            - 'fe80::/10'       # IPv6 link-local addresses
            - 'fc00::/7'        # IPv6 private addresses
    condition: selection and not 1 of filter_main_*
falsepositives:
    - Unknown
level: high
