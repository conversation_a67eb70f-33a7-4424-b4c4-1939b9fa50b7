title: Modification of ld.so.preload
id: 4b3cb710-5e83-4715-8c45-8b2b5b3e5751
status: test
description: Identifies modification of ld.so.preload for shared object injection. This technique is used by attackers to load arbitrary code into processes.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1574.006/T1574.006.md
    - https://eqllib.readthedocs.io/en/latest/analytics/fd9b987a-1101-4ed3-bda6-a70300eaf57e.html
author: <PERSON><PERSON><PERSON><PERSON> (originally from Atomic Blue Detections, <PERSON>), oscd.community
date: 2019-10-24
modified: 2021-11-27
tags:
    - attack.defense-evasion
    - attack.t1574.006
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'PATH'
        name: '/etc/ld.so.preload'
    condition: selection
falsepositives:
    - Unknown
level: high
