title: Shell Execution via Find - Linux
id: 6adfbf8f-52be-4444-9bac-81b539624146
status: test
description: |
    Detects the use of the find command to execute a shell. Such behavior may be associated with privilege escalation, unauthorized command execution, or exploitation attempt.
references:
    - https://gtfobins.github.io/gtfobins/find/#shell
    - https://www.elastic.co/guide/en/security/current/linux-restricted-shell-breakout-via-linux-binary-s.html
author: <PERSON>, <PERSON>, <PERSON>, <PERSON> (Bloomberg L.P.)
date: 2024-09-02
tags:
    - attack.discovery
    - attack.t1083
logsource:
    category: process_creation
    product: linux
detection:
    selection_img:
        Image|endswith: '/find'
        CommandLine|contains|all:
            - ' . '
            - '-exec'
    selection_cli:
        CommandLine|contains:
            - '/bin/bash'
            - '/bin/dash'
            - '/bin/fish'
            - '/bin/sh'
            - '/bin/zsh'
    condition: all of selection_*
falsepositives:
    - Unknown
level: high
