title: Rundll32 JS RunHTMLApplication Pattern
id: 9f06447a-a33a-4cbe-a94f-a3f43184a7a3
status: deprecated
description: Detects suspicious command line patterns used when rundll32 is used to run JavaScript code
references:
    - http://hyp3rlinx.altervista.org/advisories/MICROSOFT_WINDOWS_DEFENDER_DETECTION_BYPASS.txt
    - https://hyp3rlinx.altervista.org/advisories/MICROSOFT_WINDOWS_DEFENDER_TROJAN.WIN32.POWESSERE.G_MITIGATION_BYPASS_PART2.txt
author: <PERSON><PERSON><PERSON> (Nextron Systems)
date: 2022-01-14
modified: 2024-02-23
tags:
    - attack.defense_evasion
logsource:
    category: process_creation
    product: windows
detection:
    selection1:
        CommandLine|contains|all:
            - 'rundll32'
            - 'javascript'
            - '..\..\mshtml,'
            - 'RunHTMLApplication'
    selection2:
        CommandLine|contains: ';document.write();GetObject("script'
    condition: 1 of selection*
falsepositives:
    - Unlikely
level: high
