title: Suspicious Rundll32 Script in CommandLine
id: 73fcad2e-ff14-4c38-b11d-4172c8ac86c7
status: deprecated
description: Detects suspicious process related to rundll32 based on arguments
references:
    - https://gist.github.com/ryhanson/227229866af52e2d963cf941af135a52
    - https://github.com/redcanaryco/atomic-red-team/blob/cd3690b100a495885c407282d0c94c85f48a8a2e/atomics/T1218.011/T1218.011.md
author: frack113, <PERSON><PERSON> (ZETA)
date: 2021/12/04
modified: 2024/02/23
tags:
    - attack.defense_evasion
    - attack.t1218.011
logsource:
    category: process_creation
    product: windows
detection:
    selection1:
        CommandLine|contains: 'rundll32'
    selection2:
        CommandLine|contains:
            - 'mshtml,RunHTMLApplication'
            - 'mshtml,#135'
    selection3:
        CommandLine|contains:
            - 'javascript:'
            - 'vbscript:'
    condition: all of selection*
falsepositives:
    - False positives depend on scripts and administrative tools used in the monitored environment
level: medium
