title: Okta Policy Modified or Deleted
id: 1667a172-ed4c-463c-9969-efd92195319a
status: test
description: Detects when an Okta policy is modified or deleted.
references:
    - https://developer.okta.com/docs/reference/api/system-log/
    - https://developer.okta.com/docs/reference/api/event-types/
author: <PERSON> @austinsonger
date: 2021-09-12
modified: 2022-10-09
tags:
    - attack.impact
logsource:
    product: okta
    service: okta
detection:
    selection:
        eventtype:
            - policy.lifecycle.update
            - policy.lifecycle.delete
    condition: selection
falsepositives:
    - Okta Policies being modified or deleted may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Okta Policies modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: low
