title: Steganography Unzip Hidden Information From Picture File
id: edd595d7-7895-4fa7-acb3-85a18a8772ca
status: test
description: Detects extracting of zip file from image file
references:
    - https://zerotoroot.me/steganography-hiding-a-zip-in-a-jpeg-file/
author: '<PERSON><PERSON><PERSON>'
date: 2021-09-09
modified: 2022-10-09
tags:
    - attack.defense-evasion
    - attack.t1027.003
logsource:
    product: linux
    service: auditd
detection:
    commands:
        type: EXECVE
        a0: unzip
    a1:
        a1|endswith:
            - '.jpg'
            - '.png'
    condition: commands and a1
falsepositives:
    - Unknown
level: low
