title: Office Applications Spawning Wmi Cli Alternate
id: 04f5363a-6bca-42ff-be70-0d28bf629ead
status: deprecated
description: Initial execution of malicious document calls wmic to execute the file with regsvr32
references:
    - https://thedfirreport.com/2021/03/29/sodinokibi-aka-revil-ransomware/
    - https://github.com/vadim-hunter/Detection-Ideas-Rules/blob/****************************************/Threat%20Intelligence/The%20DFIR%20Report/20210329_Sodinokibi_(aka_REvil)_Ransomware.yaml
author: <PERSON><PERSON><PERSON> (ThreatIntel), <PERSON>b<PERSON>r<PERSON><PERSON> (Rule)
date: 2021/08/23
modified: 2023/02/04
tags:
    - attack.t1204.002
    - attack.t1047
    - attack.t1218.010
    - attack.execution
    - attack.defense_evasion
logsource:
    product: windows
    category: process_creation
detection:
    #useful_information: Add more office applications to the rule logic of choice
    selection1:
        - Image|endswith: '\wbem\WMIC.exe'
        - CommandLine|contains: 'wmic '
    selection2:
        ParentImage|endswith:
            - '\winword.exe'
            - '\excel.exe'
            - '\powerpnt.exe'
            - '\msaccess.exe'
            - '\mspub.exe'
            - '\eqnedt32.exe'
            - '\visio.exe'
    condition: all of selection*
falsepositives:
    - Unknown
level: high
