title: Privileged Account Creation
id: f7b5b004-dece-46e4-a4a5-f6fd0e1c6947
status: test
description: Detects when a new admin is created.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts#changes-to-privileged-accounts
author: <PERSON> '@markmorow', <PERSON><PERSON><PERSON>, '@Yochana-H', <PERSON>
date: 2022-08-11
modified: 2022-08-16
tags:
    - attack.persistence
    - attack.privilege-escalation
    - attack.t1078.004
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message|contains|all:
            - Add user
            - Add member to role
        Status: Success
    condition: selection
falsepositives:
    - A legitimate new admin account being created
level: medium
