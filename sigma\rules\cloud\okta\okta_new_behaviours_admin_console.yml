title: <PERSON><PERSON> New Admin Console Behaviours
id: a0b38b70-3cb5-484b-a4eb-c4d8e7bcc0a9
status: test
description: Detects when <PERSON><PERSON> identifies new activity in the Admin Console.
references:
    - https://developer.okta.com/docs/reference/api/system-log/
    - https://sec.okta.com/articles/2023/08/cross-tenant-impersonation-prevention-and-detection
author: kelnage
date: 2023-09-07
modified: 2024-06-26
tags:
    - attack.initial-access
    - attack.t1078.004
logsource:
    product: okta
    service: okta
detection:
    selection_event:
        eventtype: 'policy.evaluate_sign_on'
        target.displayname: 'Okta Admin Console'
    selection_positive:
        - debugcontext.debugdata.behaviors|contains: 'POSITIVE'
        - debugcontext.debugdata.logonlysecuritydata|contains: 'POSITIVE'
    condition: all of selection_*
falsepositives:
    - When an admin begins using the Admin Console and one of <PERSON><PERSON>'s heuristics incorrectly identifies the behavior as being unusual.
level: high
