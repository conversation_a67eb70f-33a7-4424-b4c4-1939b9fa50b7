[{"id": "867613fb-fa60-4497-a017-a82df74a172c", "title": "PowerShell Execution", "date": "2019-09-12", "modified": "2021-11-05", "level": "medium"}, {"id": "0d894093-71bc-43c3-8c4d-ecfc28dcf5d9", "title": "Mimikatz Detection LSASS Access", "date": "2017-10-18", "modified": "2022-04-11", "level": "high"}, {"id": "3d304fda-78aa-43ed-975c-d740798a49c1", "title": "Suspicious PowerShell Invocations - Generic", "date": "2017-03-12", "modified": "2022-04-11", "level": "high"}, {"id": "56a8189f-11b2-48c8-8ca7-c54b03c2fbf7", "title": "Suspicious Esentutl Use", "date": "2020-05-23", "modified": "2022-04-11", "level": "high"}, {"id": "65531a81-a694-4e31-ae04-f8ba5bc33759", "title": "Suspicious PowerShell Download", "date": "2017-03-05", "modified": "2022-04-11", "level": "medium"}, {"id": "9f7aa113-9da6-4a8d-907c-5f1a4b908299", "title": "SyncAppvPublishingServer Execution to Bypass Powershell Restriction", "date": "2020-10-05", "modified": "2022-04-11", "level": "medium"}, {"id": "a0d63692-a531-4912-ad39-4393325b2a9c", "title": "RClone Execution", "date": "2021-05-10", "modified": "2022-04-11", "level": "high"}, {"id": "b932b60f-fdda-4d53-8eda-a170c1d97bbd", "title": "Activity Related to NTDS.dit Domain Hash Retrieval", "date": "2019-01-16", "modified": "2022-04-11", "level": "high"}, {"id": "cb7286ba-f207-44ab-b9e6-760d82b84253", "title": "Rclone Execution via Command Line or PowerShell", "date": "2021-05-26", "modified": "2022-04-11", "level": "high"}, {"id": "fde7929d-8beb-4a4c-b922-be9974671667", "title": "SyncAppvPublishingServer Execution to Bypass Powershell Restriction", "date": "2020-10-05", "modified": "2022-04-11", "level": "medium"}, {"id": "17f878b8-9968-4578-b814-c4217fc5768c", "title": "Autorun Keys Modification", "date": "2019-10-25", "modified": "2022-05-14", "level": "medium"}, {"id": "29d31aee-30f4-4006-85a9-a4a02d65306c", "title": "Lateral Movement Indicator ConDrv", "date": "2021-04-27", "modified": "2022-05-14", "level": "low"}, {"id": "98f4c75c-3089-44f3-b733-b327b9cd9c9d", "title": "Accessing Encrypted Credentials from Google Chrome Login Database", "date": "2021-12-20", "modified": "2022-05-14", "level": "medium"}, {"id": "a457f232-7df9-491d-898f-b5aabd2cbe2f", "title": "Windows Management Instrumentation DLL Loaded Via Microsoft Word", "date": "2019-12-26", "modified": "2022-05-14", "level": "informational"}, {"id": "db2110f3-479d-42a6-94fb-d35bc1e46492", "title": "CreateMiniDump Hacktool", "date": "2019-12-22", "modified": "2022-05-14", "level": "high"}, {"id": "2621b3a6-3840-4810-ac14-a02426086171", "title": "Winword.exe Loads Suspicious DLL", "date": "2020-10-09", "modified": "2022-07-25", "level": "medium"}, {"id": "bf6c39fc-e203-45b9-9538-05397c1b4f3f", "title": "Abusing Findstr for Defense Evasion", "date": "2020-10-05", "modified": "2022-10-12", "level": "medium"}, {"id": "82a19e3a-2bfe-4a91-8c0d-5d4c98fbb719", "title": "Possible Applocker Bypass", "date": "2019-01-16", "modified": "2022-11-03", "level": "low"}, {"id": "dca91cfd-d7ab-4c66-8da7-ee57d487b35b", "title": "Process Start From Suspicious Folder", "date": "2022-02-11", "modified": "2022-11-03", "level": "low"}, {"id": "53c7cca0-2901-493a-95db-d00d6fcf0a37", "title": "Brute Force", "date": "2019-10-25", "modified": "2022-11-04", "level": "medium"}, {"id": "5f113a8f-8b61-41ca-b90f-d374fa7e4a39", "title": "Suspicious In-Memory Module Execution", "date": "2019-10-27", "modified": "2022-11-17", "level": "low"}, {"id": "f67dbfce-93bc-440d-86ad-a95ae8858c90", "title": "Suspicious Bitsadmin Job via PowerShell", "date": "2018-10-30", "modified": "2022-11-21", "level": "high"}, {"id": "9d1c72f5-43f0-4da5-9320-648cf2099dd0", "title": "Excel Proxy Executing Regsvr32 With Payload", "date": "2021-08-23", "modified": "2022-12-02", "level": "high"}, {"id": "c0e1c3d5-4381-4f18-8145-2583f06a1fe5", "title": "Excel Proxy Executing Regsvr32 With Payload Alternate", "date": "2021-08-23", "modified": "2022-12-02", "level": "high"}, {"id": "72671447-4352-4413-bb91-b85569687135", "title": "Nslookup PwSh Download Cradle", "date": "2022-09-06", "modified": "2022-12-14", "level": "medium"}, {"id": "3f07b9d1-2082-4c56-9277-613a621983cc", "title": "Accessing WinAPI in PowerShell for Credentials Dumping", "date": "2020-10-06", "modified": "2022-12-18", "level": "high"}, {"id": "e554f142-5cf3-4e55-ace9-a1b59e0def65", "title": "DCOM InternetExplorer.Application Iertutil DLL Hijack - Sysmon", "date": "2020-10-12", "modified": "2022-12-18", "level": "critical"}, {"id": "17eb8e57-9983-420d-ad8a-2c4976c22eb8", "title": "MavInject Process Injection", "date": "2018-12-12", "modified": "2022-12-19", "level": "high"}, {"id": "36c5146c-d127-4f85-8e21-01bf62355d5a", "title": "Invoke-Obfuscation Via Use Rundll32", "date": "2019-10-08", "modified": "2022-12-30", "level": "high"}, {"id": "6d3f1399-a81c-4409-aff3-1ecfe9330baf", "title": "PrintNightmare Powershell Exploitation", "date": "2021-08-09", "modified": "2023-01-02", "level": "high"}, {"id": "83083ac6-1816-4e76-97d7-59af9a9ae46e", "title": "AzureHound PowerShell Commands", "date": "2021-10-23", "modified": "2023-01-02", "level": "high"}, {"id": "a85cf4e3-56ee-4e79-adeb-789f8fb209a8", "title": "Indirect Command Exectuion via Forfiles", "date": "2022-10-17", "modified": "2023-01-04", "level": "medium"}, {"id": "fa47597e-90e9-41cd-ab72-c3b74cfb0d02", "title": "Indirect Command Execution", "date": "2019-10-24", "modified": "2023-01-04", "level": "low"}, {"id": "e4b63079-6198-405c-abd7-3fe8b0ce3263", "title": "Suspicious CLR Logs Creation", "date": "2020-10-12", "modified": "2023-01-05", "level": "high"}, {"id": "cd5c8085-4070-4e22-908d-a5b3342deb74", "title": "Suspicious Bitstransfer via PowerShell", "date": "2021-08-19", "modified": "2023-01-10", "level": "medium"}, {"id": "d178a2d7-129a-4ba4-8ee6-d6e1fecd5d20", "title": "Renamed PowerShell", "date": "2019-08-22", "modified": "2023-01-18", "level": "high"}, {"id": "d4d2574f-ac17-4d9e-b986-aeeae0dc8fe2", "title": "Renamed Rundll32.exe Execution", "date": "2022-06-08", "modified": "2023-01-18", "level": "high"}, {"id": "e31f89f7-36fb-4697-8ab6-48823708353b", "title": "Suspicious Cmd Execution via WMI", "date": "2022-09-27", "modified": "2023-01-19", "level": "medium"}, {"id": "bf7286e7-c0be-460b-a7e8-5b2e07ecc2f2", "title": "Netcat The Powershell Version - PowerShell Module", "date": "2021-07-21", "modified": "2023-01-20", "level": "medium"}, {"id": "47688f1b-9f51-4656-b013-3cc49a166a36", "title": "Base64 Encoded Listing of Shadowcopy", "date": "2022-03-01", "modified": "2023-01-30", "level": "high"}, {"id": "5b572dcf-254b-425c-a8c5-d9af6bea35a6", "title": "Potential Xor Encoded PowerShell Command", "date": "2022-07-06", "modified": "2023-01-30", "level": "medium"}, {"id": "fd6e2919-3936-40c9-99db-0aa922c356f7", "title": "Malicious Base64 Encoded Powershell Invoke Cmdlets", "date": "2022-05-31", "modified": "2023-01-30", "level": "high"}, {"id": "eeb66bbb-3dde-4582-815a-584aee9fe6d1", "title": "Correct Execution of Nltest.exe", "date": "2021-10-04", "modified": "2023-02-02", "level": "high"}, {"id": "0acaad27-9f02-4136-a243-c357202edd74", "title": "Ryuk Ransomware Command Line Activity", "date": "2019-08-06", "modified": "2023-02-03", "level": "critical"}, {"id": "4f927692-68b5-4267-871b-073c45f4f6fe", "title": "PowerShell AMSI Bypass Pattern", "date": "2022-11-04", "modified": "2023-02-03", "level": "high"}, {"id": "038cd51c-3ad8-41c5-ba8f-5d1c92f3cc1e", "title": "Registry Dump of SAM Creds and Secrets", "date": "2022-01-05", "modified": "2023-02-04", "level": "high"}, {"id": "04f5363a-6bca-42ff-be70-0d28bf629ead", "title": "Office Applications Spawning Wmi Cli Alternate", "date": "2021-08-23", "modified": "2023-02-04", "level": "high"}, {"id": "23daeb52-e6eb-493c-8607-c4f0246cb7d8", "title": "New Lolbin Process by Office Applications", "date": "2021-08-23", "modified": "2023-02-04", "level": "high"}, {"id": "518643ba-7d9c-4fa5-9f37-baed36059f6a", "title": "WMI Execution Via Office Process", "date": "2021-08-23", "modified": "2023-02-04", "level": "medium"}, {"id": "77815820-246c-47b8-9741-e0def3f57308", "title": "Domain Trust Discovery", "date": "2019-10-23", "modified": "2023-02-04", "level": "medium"}, {"id": "4d6c9da1-318b-4edf-bcea-b6c93fa98fd0", "title": "Credential Acquisition via Registry Hive Dumping", "date": "2022-10-04", "modified": "2023-02-06", "level": "high"}, {"id": "6545ce61-a1bd-4119-b9be-fcbee42c0cf3", "title": "Execute MSDT.EXE Using Diagcab File", "date": "2022-06-09", "modified": "2023-02-06", "level": "high"}, {"id": "9841b233-8df8-4ad7-9133-b0b4402a9014", "title": "Sysinternals SDelete Registry Keys", "date": "2020-05-02", "modified": "2023-02-07", "level": "medium"}, {"id": "09af397b-c5eb-4811-b2bb-08b3de464ebf", "title": "WMI Reconnaissance List Remote Services", "date": "2022-01-01", "modified": "2023-02-14", "level": "medium"}, {"id": "7b0666ad-3e38-4e3d-9bab-78b06de85f7b", "title": "Renamed PaExec Execution", "date": "2019-04-17", "modified": "2023-02-14", "level": "medium"}, {"id": "bc3cc333-48b9-467a-9d1f-d44ee594ef48", "title": "SCM DLL Sideload", "date": "2022-12-01", "modified": "2023-02-14", "level": "medium"}, {"id": "e42af9df-d90b-4306-b7fb-05c863847ebd", "title": "WMI Remote Command Execution", "date": "2022-03-13", "modified": "2023-02-14", "level": "medium"}, {"id": "fa4b21c9-0057-4493-b289-2556416ae4d7", "title": "Squirrel <PERSON><PERSON>", "date": "2019-11-12", "modified": "2023-02-14", "level": "medium"}, {"id": "e011a729-98a6-4139-b5c4-bf6f6dd8239a", "title": "Suspicious Certutil Command Usage", "date": "2019-01-16", "modified": "2023-02-15", "level": "high"}, {"id": "034affe8-6170-11ec-844f-0f78aa0c4d66", "title": "Mimikatz MemSSP Default Log File Creation", "date": "2021-12-20", "modified": "2023-02-16", "level": "critical"}, {"id": "7fe71fc9-de3b-432a-8d57-8c809efc10ab", "title": "New Service Creation", "date": "2019-10-21", "modified": "2023-02-20", "level": "low"}, {"id": "056a7ee1-4853-4e67-86a0-3fd9ceed7555", "title": "Invoke-Obfuscation RUNDLL LAUNCHER", "date": "2020-10-18", "modified": "2023-02-21", "level": "medium"}, {"id": "3ede524d-21cc-472d-a3ce-d21b568d8db7", "title": "PsExec Service Start", "date": "2018-03-13", "modified": "2023-02-28", "level": "low"}, {"id": "80167ada-7a12-41ed-b8e9-aa47195c66a1", "title": "<PERSON> as SYSTEM", "date": "2019-10-23", "modified": "2023-02-28", "level": "high"}, {"id": "fa91cc36-24c9-41ce-b3c8-3bbc3f2f67ba", "title": "PsExec Tool Execution", "date": "2017-06-12", "modified": "2023-02-28", "level": "low"}, {"id": "2c0d2d7b-30d6-4d14-9751-7b9113042ab9", "title": "Suspicious Characters in CommandLine", "date": "2022-04-27", "modified": "2023-03-03", "level": "high"}, {"id": "6783aa9e-0dc3-49d4-a94a-8b39c5fd700b", "title": "Stop Or Remove Antivirus Service", "date": "2021-07-07", "modified": "2023-03-04", "level": "high"}, {"id": "7fd4bb39-12d0-45ab-bb36-cebabc73dc7b", "title": "Suspicious Execution of Sc to Delete AV Services", "date": "2022-08-01", "modified": "2023-03-04", "level": "high"}, {"id": "a7a7e0e5-1d57-49df-9c58-9fe5bc0346a2", "title": "Renamed PsE<PERSON>c", "date": "2019-05-21", "modified": "2023-03-04", "level": "high"}, {"id": "1a70042a-6622-4a2b-8958-267625349abf", "title": "Run from a Zip File", "date": "2021-12-26", "modified": "2023-03-05", "level": "medium"}, {"id": "46591fae-7a4c-46ea-aec3-dff5e6d785dc", "title": "Root Certificate Installed", "date": "2020-10-10", "modified": "2023-03-05", "level": "medium"}, {"id": "eb87818d-db5d-49cc-a987-d5da331fbd90", "title": "Stop Windows Service", "date": "2019-10-23", "modified": "2023-03-05", "level": "low"}, {"id": "23250293-eed5-4c39-b57a-841c8933a57d", "title": "Visual Basic Script Execution", "date": "2022-01-02", "modified": "2023-03-06", "level": "medium"}, {"id": "344482e4-a477-436c-aa70-7536d18a48c7", "title": "Execution via MSSQL Xp_cmdshell Stored Procedure", "date": "2022-09-28", "modified": "2023-03-06", "level": "high"}, {"id": "00a4bacd-6db4-46d5-9258-a7d5ebff4003", "title": "Read and Execute a File Via Cmd.exe", "date": "2022-08-20", "modified": "2023-03-07", "level": "medium"}, {"id": "70e68156-6571-427b-a6e9-4476a173a9b6", "title": "Cmd Stream Redirection", "date": "2022-02-04", "modified": "2023-03-07", "level": "medium"}, {"id": "033fe7d6-66d1-4240-ac6b-28908009c71f", "title": "APT29", "date": "2018-12-04", "modified": "2023-03-08", "level": "high"}, {"id": "04d9079e-3905-4b70-ad37-6bdf11304965", "title": "CrackMapExecWin", "date": "2018-04-08", "modified": "2023-03-08", "level": "critical"}, {"id": "18739897-21b1-41da-8ee4-5b786915a676", "title": "GALLIUM Artefacts", "date": "2020-02-07", "modified": "2023-03-09", "level": "high"}, {"id": "0eb2107b-a596-422e-b123-b389d5594ed7", "title": "Hurricane Panda Activity", "date": "2019-03-04", "modified": "2023-03-10", "level": "high"}, {"id": "36222790-0d43-4fe8-86e4-674b27809543", "title": "DNS Tunnel Technique from MuddyWater", "date": "2020-06-04", "modified": "2023-03-10", "level": "critical"}, {"id": "4a12fa47-c735-4032-a214-6fab5b120670", "title": "Lazarus Activity Apr21", "date": "2021-04-20", "modified": "2023-03-10", "level": "high"}, {"id": "7b49c990-4a9a-4e65-ba95-47c9cc448f6e", "title": "<PERSON>s", "date": "2020-12-23", "modified": "2023-03-10", "level": "critical"}, {"id": "43f487f0-755f-4c2a-bce7-d6d2eec2fcf8", "title": "Suspicious Add Scheduled Task From User AppData Temp", "date": "2021-11-03", "modified": "2023-03-14", "level": "high"}, {"id": "d813d662-785b-42ca-8b4a-f7457d78d5a9", "title": "Suspicious Load of Advapi31.dll", "date": "2022-02-03", "modified": "2023-03-15", "level": "informational"}, {"id": "e74e15cc-c4b6-4c80-b7eb-dfe49feb7fe9", "title": "Edit of .bash_profile and .bashrc", "date": "2019-05-12", "modified": "2023-03-23", "level": "medium"}, {"id": "ba2a7c80-027b-460f-92e2-57d113897dbc", "title": "App Permissions Granted For Other APIs", "date": "2022-07-28", "modified": "2023-03-29", "level": "medium"}, {"id": "18cf6cf0-39b0-4c22-9593-e244bdc9a2d4", "title": "TA505 Dropper Load Pattern", "date": "2020-12-08", "modified": "2023-04-05", "level": "critical"}, {"id": "2d117e49-e626-4c7c-bd1f-c3c0147774c8", "title": "Potential PowerShell Base64 Encoded Shellcode", "date": "2018-11-17", "modified": "2023-04-06", "level": "medium"}, {"id": "635dbb88-67b3-4b41-9ea5-a3af2dd88153", "title": "Microsoft Binary Github Communication", "date": "2017-08-24", "modified": "2023-04-18", "level": "high"}, {"id": "6c939dfa-c710-4e12-a4dd-47e1f10e68e1", "title": "Domestic Kitten FurBall Malware Pattern", "date": "2021-02-08", "modified": "2023-04-20", "level": "high"}, {"id": "6355a919-2e97-4285-a673-74645566340d", "title": "Process Memory Dumped Via RdrLeakDiag.EXE", "date": "2022-01-04", "modified": "2023-04-24", "level": "high"}, {"id": "9cf01b6c-e723-4841-a868-6d7f8245ca6e", "title": "Group Modification Logging", "date": "2019-03-26", "modified": "2023-04-26", "level": "low"}, {"id": "410ad193-a728-4107-bc79-4419789fcbf8", "title": "Trickbot Malware Reconnaissance Activity", "date": "2019-12-28", "modified": "2023-04-28", "level": "high"}, {"id": "fce5f582-cc00-41e1-941a-c6fabf0fdb8c", "title": "Suspicious PowerShell Invocations - Specific", "date": "2017-03-05", "modified": "2023-05-04", "level": "high"}, {"id": "f016c716-754a-467f-a39e-63c06f773987", "title": "Suspicious Remote Thread Target", "date": "2022-08-25", "modified": "2023-05-05", "level": "medium"}, {"id": "65d2be45-8600-4042-b4c0-577a1ff8a60e", "title": "Application Whitelisting Bypass via DLL Loaded by odbcconf.exe", "date": "2019-10-25", "modified": "2023-05-22", "level": "medium"}, {"id": "8e2b24c9-4add-46a0-b4bb-0057b4e6187d", "title": "Regsvr32 Anomaly", "date": "2019-01-16", "modified": "2023-05-26", "level": "high"}, {"id": "fe6e002f-f244-4278-9263-20e4b593827f", "title": "Alternate PowerShell Hosts - Image", "date": "2019-09-12", "modified": "2023-06-01", "level": "low"}, {"id": "9e77ed63-2ecf-4c7b-b09d-640834882028", "title": "PsExec Pipes Artifacts", "date": "2020-05-10", "modified": "2023-08-07", "level": "medium"}, {"id": "39776c99-1c7b-4ba0-b5aa-641525eee1a4", "title": "Execution via CL_Mutexverifiers.ps1", "date": "2020-10-14", "modified": "2023-08-17", "level": "high"}, {"id": "4cd29327-685a-460e-9dac-c3ab96e549dc", "title": "Execution via CL_Invocation.ps1 - Powershell", "date": "2020-10-14", "modified": "2023-08-17", "level": "high"}, {"id": "4e8d5fd3-c959-441f-a941-f73d0cdcdca5", "title": "Abusing Windows Telemetry For Persistence - Registry", "date": "2020-09-29", "modified": "2023-08-17", "level": "high"}, {"id": "7c637634-c95d-4bbf-b26c-a82510874b34", "title": "Disable Microsoft Office Security Features", "date": "2021-06-08", "modified": "2023-08-17", "level": "high"}, {"id": "8a58209c-7ae6-4027-afb0-307a78e4589a", "title": "User Account Hidden By Registry", "date": "2022-08-20", "modified": "2023-08-17", "level": "high"}, {"id": "a166f74e-bf44-409d-b9ba-ea4b2dd8b3cd", "title": "Office Security Settings Changed", "date": "2020-05-22", "modified": "2023-08-17", "level": "high"}, {"id": "c81fe886-cac0-4913-a511-2822d72ff505", "title": "SilentProcessExit Monitor Registration", "date": "2021-02-26", "modified": "2023-08-17", "level": "high"}, {"id": "0c1ffcf9-efa9-436e-ab68-23a9496ebf5b", "title": "User Added To Admin Group - MacOS", "date": "2023-03-19", "modified": "2023-08-22", "level": "medium"}, {"id": "5b80cf53-3a46-4adc-960b-05ec19348d74", "title": "Wscript Execution from Non C Drive", "date": "2022-10-01", "modified": "2023-08-29", "level": "medium"}, {"id": "5e3d3601-0662-4af0-b1d2-36a05e90c40a", "title": "LSASS Memory Dump File Creation", "date": "2019-10-22", "modified": "2023-08-29", "level": "high"}, {"id": "39b64854-5497-4b57-a448-40977b8c9679", "title": "Vulnerable Driver Load By Name", "date": "2022-10-03", "modified": "2023-09-03", "level": "low"}, {"id": "21b23707-60d6-41bb-96e3-0f0481b0fed9", "title": "Vulnerable Dell BIOS Update Driver Load", "date": "2021-05-05", "modified": "2023-09-12", "level": "high"}, {"id": "7bcfeece-e5ed-4ff3-a5fb-2640d8cc8647", "title": "Vulnerable GIGABYTE Driver Load", "date": "2022-07-25", "modified": "2023-09-12", "level": "high"}, {"id": "7c676970-af4f-43c8-80af-ec9b49952852", "title": "Vulnerable AVAST Anti Rootkit Driver Load", "date": "2022-07-28", "modified": "2023-09-12", "level": "high"}, {"id": "9bacc538-d1b9-4d42-862e-469eafc05a41", "title": "Vulnerable HW Driver Load", "date": "2022-07-26", "modified": "2023-09-12", "level": "high"}, {"id": "ac683a42-877b-4ff8-91ac-69e94b0f70b4", "title": "Vulnerable Lenovo Driver Load", "date": "2022-11-10", "modified": "2023-09-12", "level": "high"}, {"id": "91bc09e7-674d-4cf5-8d86-ed5d8bdb95a6", "title": "Usage Of Malicious POORTRY Signed Driver", "date": "2022-12-16", "modified": "2023-09-13", "level": "high"}, {"id": "d7825193-b70a-48a4-b992-8b5b3015cc11", "title": "Windows Update Client LOLBIN", "date": "2020-10-17", "modified": "2023-11-11", "level": "high"}, {"id": "ca83e9f3-657a-45d0-88d6-c1ac280caf53", "title": "New Service Uses Double Ampersand in Path", "date": "2022-07-05", "modified": "2023-11-15", "level": "high"}, {"id": "fe34868f-6e0e-4882-81f6-c43aa8f15b62", "title": "Windows Defender Threat Detection Disabled", "date": "2020-07-28", "modified": "2023-11-22", "level": "high"}, {"id": "32d0d3e2-e58d-4d41-926b-18b520b2b32d", "title": "Credential Dumping Tools Accessing LSASS Memory", "date": "2017-02-16", "modified": "2023-11-30", "level": "high"}, {"id": "a122ac13-daf8-4175-83a2-72c387be339d", "title": "Security Event Log Cleared", "date": "2021-08-15", "modified": "2023-12-06", "level": "medium"}, {"id": "0332a266-b584-47b4-933d-a00b103e1b37", "title": "Suspicious Get-WmiObject", "date": "2022-01-12", "modified": "2023-12-11", "level": "low"}, {"id": "46deb5e1-28c9-4905-b2df-51cdcc9e6073", "title": "PowerShell Scripts Run by a Services", "date": "2020-10-06", "modified": "2023-12-11", "level": "high"}, {"id": "d23f2ba5-9da0-4463-8908-8ee47f614bb9", "title": "Powershell File and Directory Discovery", "date": "2021-12-15", "modified": "2023-12-11", "level": "low"}, {"id": "df5ff0a5-f83f-4a5b-bba1-3e6a3f6f6ea2", "title": "Credential Dumping Tools Service Execution", "date": "2017-03-05", "modified": "2023-12-11", "level": "critical"}, {"id": "602a1f13-c640-4d73-b053-be9a2fa58b77", "title": "Svchost DLL Search Order Hijack", "date": "2019-10-28", "modified": "2024-01-10", "level": "high"}, {"id": "839dd1e8-eda8-4834-8145-01beeee33acd", "title": "SAM Dump to AppData", "date": "2018-01-27", "modified": "2024-01-18", "level": "high"}, {"id": "e32ce4f5-46c6-4c47-ba69-5de3c9193cd7", "title": "Possible Process Hollowing Image Loading", "date": "2018-01-07", "modified": "2024-01-22", "level": "high"}, {"id": "a6d67db4-6220-436d-8afc-f3842fe05d43", "title": "Dnscat Execution", "date": "2019-10-24", "modified": "2024-01-25", "level": "critical"}, {"id": "d7b09985-95a3-44be-8450-b6eadf49833e", "title": "Suspicious Non-Browser Network Communication With Reddit API", "date": "2023-02-16", "modified": "2024-02-02", "level": "medium"}, {"id": "37325383-740a-403d-b1a2-b2b4ab7992e7", "title": "CobaltStrike Malleable (OCSP) Profile", "date": "2019-11-12", "modified": "2024-02-15", "level": "high"}, {"id": "41b42a36-f62c-4c34-bd40-8cb804a34ad8", "title": "CobaltStrike Malformed UAs in Malleable Profiles", "date": "2021-05-06", "modified": "2024-02-15", "level": "critical"}, {"id": "953b895e-5cc9-454b-b183-7f3db555452e", "title": "CobaltStrike Malleable Amazon Browsing Traffic Profile", "date": "2019-11-12", "modified": "2024-02-15", "level": "high"}, {"id": "c9b33401-cc6a-4cf6-83bb-57ddcb2407fc", "title": "CobaltStrike Malleable OneDrive Browsing Traffic Profile", "date": "2019-11-12", "modified": "2024-02-15", "level": "high"}, {"id": "73fcad2e-ff14-4c38-b11d-4172c8ac86c7", "title": "Suspicious Rundll32 Script in CommandLine", "date": "2021-12-04", "modified": "2024-02-23", "level": "medium"}, {"id": "9f06447a-a33a-4cbe-a94f-a3f43184a7a3", "title": "Rundll32 JS RunHTMLApplication Pattern", "date": "2022-01-14", "modified": "2024-02-23", "level": "high"}, {"id": "e06ac91d-b9e6-443d-8e5b-af749e7aa6b6", "title": "iOS Implant URL Pattern", "date": "2019-08-30", "modified": "2024-02-26", "level": "critical"}, {"id": "628d7a0b-7b84-4466-8552-e6138bc03b43", "title": "Suspicious Epmap Connection", "date": "2022-07-14", "modified": "2024-03-01", "level": "high"}, {"id": "9433ff9c-5d3f-4269-99f8-95fc826ea489", "title": "CrackMapExec File Creation Patterns", "date": "2022-03-12", "modified": "2024-03-01", "level": "high"}, {"id": "277dc340-0540-42e7-8efb-5ff460045e07", "title": "Service Binary in Uncommon Folder", "date": "2022-05-02", "modified": "2024-03-25", "level": "medium"}, {"id": "42f0e038-767e-4b85-9d96-2c6335bad0b5", "title": "Adwind RAT / JRAT - Registry", "date": "2017-11-10", "modified": "2024-03-26", "level": "high"}, {"id": "5039f3d2-406a-4c1a-9350-7a5a85dc84c2", "title": "Search-ms and WebDAV Suspicious Indicators in URL", "date": "2023-08-21", "modified": "2024-05-10", "level": "high"}, {"id": "b916cba1-b38a-42da-9223-17114d846fd6", "title": "Potential NT API Stub Patching", "date": "2023-01-07", "modified": "2024-05-27", "level": "medium"}, {"id": "3d968d17-ffa4-4bc0-bfdc-f139de76ce77", "title": "Potential Persistence Via COM Hijacking From Suspicious Locations", "date": "2022-07-28", "modified": "2024-07-16", "level": "high"}, {"id": "1a3d42dd-3763-46b9-8025-b5f17f340dfb", "title": "Suspicious Unattend.xml File Access", "date": "2021-12-19", "modified": "2024-07-22", "level": "medium"}, {"id": "6902955a-01b7-432c-b32a-6f5f81d8f624", "title": "Suspicious File Event With Teams Objects", "date": "2022-09-16", "modified": "2024-07-22", "level": "high"}, {"id": "a0ff33d8-79e4-4cef-b4f3-9dc4133ccd12", "title": "Potential Persistence Via COM Search Order Hijacking", "date": "2020-04-14", "modified": "2024-09-02", "level": "medium"}, {"id": "a33f8808-2812-4373-ae95-8cfb82134978", "title": "Windows Defender Exclusion Deleted", "date": "2019-10-26", "modified": "2025-01-30", "level": "medium"}, {"id": "e17121b4-ef2a-4418-8a59-12fb1631fa9e", "title": "Delete Volume Shadow Copies via WMI with PowerShell - PS Script", "date": "2021-12-26", "modified": "2025-05-20", "level": "high"}]