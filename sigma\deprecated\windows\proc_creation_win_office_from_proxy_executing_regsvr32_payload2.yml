title: Excel Proxy Executing Regsvr32 With Payload Alternate
id: c0e1c3d5-4381-4f18-8145-2583f06a1fe5
status: deprecated
description: |
  Excel called wmic to finally proxy execute regsvr32 with the payload.
  An attacker wanted to break suspicious parent-child chain (Office app spawns LOLBin).
  But we have command-line in the event which allow us to "restore" this suspicious parent-child chain and detect it.
  Monitor process creation with "wmic process call create" and LOLBins in command-line with parent Office application processes.
references:
    - https://thedfirreport.com/2021/03/29/sodinokibi-aka-revil-ransomware/
    - https://github.com/vadim-hunter/Detection-Ideas-Rules/blob/****************************************/Threat%20Intelligence/The%20DFIR%20Report/20210329_Sodinokibi_(aka_REvil)_Ransomware.yaml
author: '<PERSON><PERSON><PERSON> (ThreatIntel), <PERSON>b<PERSON><PERSON><PERSON><PERSON> (Rule)'
date: 2021/08/23
modified: 2022/12/02
tags:
    - attack.t1204.002
    - attack.t1047
    - attack.t1218.010
    - attack.execution
    - attack.defense_evasion
logsource:
    product: windows
    category: process_creation
detection:
    #useful_information: add more LOLBins to the rules logic of your choice.
    selection1:
        CommandLine|contains:
            - 'regsvr32'
            - 'rundll32'
            - 'msiexec'
            - 'mshta'
            - 'verclsid'
    selection2:
        - Image|endswith: '\wbem\WMIC.exe'
        - CommandLine|contains: 'wmic '
    selection3:
        ParentImage|endswith:
            - '\winword.exe'
            - '\excel.exe'
            - '\powerpnt.exe'
    selection4:
        CommandLine|contains|all:
            - 'process'
            - 'create'
            - 'call'
    condition: all of selection*
falsepositives:
    - Unknown
level: high
