title: Antivirus Web Shell Detection
id: fdf135a2-9241-4f96-a114-bb404948f736
status: test
description: |
    Detects a highly relevant Antivirus alert that reports a web shell.
    It's highly recommended to tune this rule to the specific strings used by your anti virus solution by downloading a big WebShell repository from e.g. github and checking the matches.
    This event must not be ignored just because the AV has blocked the malware but investigate, how it came there in the first place.
references:
    - https://www.nextron-systems.com/?s=antivirus
    - https://github.com/tennc/webshell
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
author: Florian Roth (Nextron Systems), Arnim Rupp
date: 2018-09-09
modified: 2024-11-02
tags:
    - attack.persistence
    - attack.t1505.003
logsource:
    category: antivirus
detection:
    selection:
        - Signature|startswith:
              - 'ASP.'
              - 'IIS/BackDoor'
              - 'JAVA/Backdoor'
              - 'JSP.'
              - 'Perl.'
              - 'PHP.'
              - 'Troj/ASP'
              - 'Troj/JSP'
              - 'Troj/PHP'
              - 'VBS/Uxor' # looking for 'VBS/' would also find downloader's and droppers meant for desktops
        - Signature|contains:
              - 'ASP_' # looking for 'VBS_' would also find downloader's and droppers meant for desktops
              - 'ASP:'
              - 'ASP.Agent'
              - 'ASP/'
              # - 'ASP/Agent'
              - 'Aspdoor'
              - 'ASPXSpy'
              - 'Backdoor.ASP'
              - 'Backdoor.Java'
              - 'Backdoor.JSP'
              - 'Backdoor.PHP'
              - 'Backdoor.VBS'
              - 'Backdoor/ASP'
              - 'Backdoor/Java'
              - 'Backdoor/JSP'
              - 'Backdoor/PHP'
              - 'Backdoor/VBS'
              - 'C99shell'
              - 'Chopper'
              - 'filebrowser'
              - 'JSP_'
              - 'JSP:'
              - 'JSP.Agent'
              - 'JSP/'
              # - 'JSP/Agent'
              - 'Perl:'
              - 'Perl/'
              - 'PHP_'
              - 'PHP:'
              - 'PHP.Agent'
              - 'PHP/'
              # - 'PHP/Agent'
              - 'PHPShell'
              - 'PShlSpy'
              - 'SinoChoper'
              - 'Trojan.ASP'
              - 'Trojan.JSP'
              - 'Trojan.PHP'
              - 'Trojan.VBS'
              - 'VBS.Agent'
              - 'VBS/Agent'
              - 'Webshell'
    condition: selection
falsepositives:
    - Unlikely
level: high
