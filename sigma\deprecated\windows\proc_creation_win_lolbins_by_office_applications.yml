title: New Lolbin Process by Office Applications
id: 23daeb52-e6eb-493c-8607-c4f0246cb7d8
status: deprecated
description: This rule will monitor any office apps that spins up a new LOLBin process. This activity is pretty suspicious and should be investigated.
references:
    - https://thedfirreport.com/2021/03/29/sodinokibi-aka-revil-ransomware/
    - https://doublepulsar.com/follina-a-microsoft-office-code-execution-vulnerability-1a47fce5629e
    - https://github.com/vadim-hunter/Detection-Ideas-Rules/blob/****************************************/Threat%20Intelligence/The%20DFIR%20Report/20210329_Sodinokibi_(aka_REvil)_Ransomware.yaml
    - https://github.com/splunk/security_content/blob/develop/detections/endpoint/office_spawning_control.yml
    - https://twitter.com/andythevariable/status/1576953781581144064?s=20&t=QiJILvK4ZiBdR8RJe24u-A
    - https://www.elastic.co/security-labs/exploring-the-ref2731-intrusion-set
author: 'Vadim Khrykov (ThreatIntel), Cyb3rEng (Rule), Michael Haag, Christopher Peacock @securepeacock (Update), SCYTHE @scythe_io (Update)'
date: 2021/08/23
modified: 2023/02/04
tags:
    - attack.t1204.002
    - attack.t1047
    - attack.t1218.010
    - attack.execution
    - attack.defense_evasion
logsource:
    product: windows
    category: process_creation
detection:
    #useful_information: add more LOLBins to the rules logic of your choice.
    selection:
        Image|endswith:
            - '\regsvr32.exe'
            - '\rundll32.exe'
            - '\msiexec.exe'
            - '\mshta.exe'
            - '\verclsid.exe'
            - '\msdt.exe'
            - '\control.exe'
            - '\msidb.exe'
        ParentImage|endswith:
            - '\winword.exe'
            - '\excel.exe'
            - '\powerpnt.exe'
            - '\msaccess.exe'
            - '\mspub.exe'
            - '\eqnedt32.exe'
            - '\visio.exe'
            - '\wordpad.exe'
            - '\wordview.exe'
    condition: selection
falsepositives:
    - Unknown
level: high
