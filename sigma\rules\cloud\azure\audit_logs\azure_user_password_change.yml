title: Password Reset By User Account
id: 340ee172-4b67-4fb4-832f-f961bdc1f3aa
status: test
description: Detect when a user has reset their password in Azure AD
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts
author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, '@Yochana-H'
date: 2022-08-03
tags:
    - attack.persistence
    - attack.credential-access
    - attack.t1078.004
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        Category: 'UserManagement'
        Status: 'Success'
        Initiatedby: 'UPN'
    filter:
        Target|contains: 'UPN'
        ActivityType|contains: 'Password reset'
    condition: selection and filter
falsepositives:
    - If this was approved by System Administrator or confirmed user action.
level: medium
