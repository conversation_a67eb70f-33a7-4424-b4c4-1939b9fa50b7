title: Suspicious Bitsadmin Job via PowerShell
id: f67dbfce-93bc-440d-86ad-a95ae8858c90
status: deprecated
description: Detect download by BITS jobs via PowerShell
references:
    - https://eqllib.readthedocs.io/en/latest/analytics/ec5180c9-721a-460f-bddc-27539a284273.html
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1197/T1197.md
author: Endgame, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ported to sigma for oscd.community)
date: 2018/10/30
modified: 2022/11/21
tags:
    - attack.defense_evasion
    - attack.persistence
    - attack.t1197
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        Image|endswith:
            - '\powershell.exe'
            - '\pwsh.exe'
        CommandLine|contains: 'Start-BitsTransfer'
    condition: selection
fields:
    - ComputerName
    - User
    - CommandLine
falsepositives:
    - Unknown
level: high
