title: Potential Remote Command Execution In Pod Container
id: a1b0ca4e-7835-413e-8471-3ff2b8a66be6
status: test
description: |
    Detects attempts to execute remote commands, within a Pod's container using e.g. the "kubectl exec" command.
references:
    - https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/Exec%20into%20container/
author: <PERSON> (@laripping)
date: 2024-03-26
tags:
    - attack.t1609
    - attack.execution
logsource:
    category: application
    product: kubernetes
    service: audit
detection:
    selection:
        verb: 'create'
        objectRef.resource: 'pods'
        objectRef.subresource: 'exec'
    condition: selection
falsepositives:
    - Legitimate debugging activity. Investigate the identity performing the requests and their authorization.
level: medium
