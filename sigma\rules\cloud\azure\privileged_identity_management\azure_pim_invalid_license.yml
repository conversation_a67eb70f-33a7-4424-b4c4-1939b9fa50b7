title: Invalid PIM License
id: 58af08eb-f9e1-43c8-9805-3ad9b0482bd8
status: test
description: Identifies when an organization doesn't have the proper license for PIM and is out of compliance.
references:
    - https://learn.microsoft.com/en-us/entra/id-governance/privileged-identity-management/pim-how-to-configure-security-alerts#the-organization-doesnt-have-microsoft-entra-premium-p2-or-microsoft-entra-id-governance
author: <PERSON> '@markmorow', <PERSON>, '@gleeiamglo'
date: 2023-09-14
tags:
    - attack.t1078
    - attack.persistence
    - attack.privilege-escalation
logsource:
    product: azure
    service: pim
detection:
    selection:
        riskEventType: 'invalidLicenseAlertIncident'
    condition: selection
falsepositives:
    - Investigate if licenses have expired.
level: high
