title: <PERSON> Loaders
id: 7b49c990-4a9a-4e65-ba95-47c9cc448f6e
status: deprecated
description: Detects different loaders as described in various threat reports on Lazarus group activity
references:
    - https://www.hvs-consulting.de/lazarus-report/
    - https://securelist.com/lazarus-covets-covid-19-related-intelligence/99906/
author: <PERSON><PERSON><PERSON> (Nextron Systems), wagga
date: 2020/12/23
modified: 2023/03/10
tags:
    - attack.g0032
    - attack.execution
    - attack.t1059
logsource:
    category: process_creation
    product: windows
detection:
    selection_cmd1:
        CommandLine|contains|all:
            - 'cmd.exe /c '
            - ' -p 0x'
    selection_cmd2:
        CommandLine|contains:
            - 'C:\ProgramData\'
            - 'C:\RECYCLER\'
    selection_rundll1:
        CommandLine|contains|all:
            - 'rundll32.exe '
            - 'C:\ProgramData\'
    selection_rundll2:
        CommandLine|contains:
            - '.bin,'
            - '.tmp,'
            - '.dat,'
            - '.io,'
            - '.ini,'
            - '.db,'
    condition: ( selection_cmd1 and selection_cmd2 ) or ( selection_rundll1 and selection_rundll2 )
falsepositives:
    - Unknown
level: critical
