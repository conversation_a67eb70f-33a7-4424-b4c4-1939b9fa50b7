title: Service Reload or Start - Linux
id: 2625cc59-0634-40d0-821e-cb67382a3dd7
status: test
description: Detects the start, reload or restart of a service.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1543.002/T1543.002.md
author: <PERSON>, oscd.community, CheraghiMilad
date: 2019-09-23
modified: 2025-03-03
tags:
    - attack.persistence
    - attack.t1543.002
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'EXECVE'
        a0|contains:
            - 'systemctl'
            - 'service'
        a1|contains:
            - 'reload'
            - 'start'
    condition: selection
falsepositives:
    - Installation of legitimate service.
    - Legitimate reconfiguration of service.
    - Command line contains daemon-reload.
level: low
