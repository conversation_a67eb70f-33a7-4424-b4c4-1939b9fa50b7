title: Account Lockout
id: 2b7d6fc0-71ac-4cf7-8ed1-b5788ee5257a
status: test
description: Identifies user account which has been locked because the user tried to sign in too many times with an incorrect user ID or password.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts
author: AlertIQ
date: 2021-10-10
modified: 2022-12-25
tags:
    - attack.credential-access
    - attack.t1110
logsource:
    product: azure
    service: signinlogs
detection:
    selection:
        ResultType: 50053
    condition: selection
falsepositives:
    - Unknown
level: medium
