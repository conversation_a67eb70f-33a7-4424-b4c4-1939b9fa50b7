title: <PERSON><PERSON> Accounts In A Privileged Role
id: e402c26a-267a-45bd-9615-bd9ceda6da85
status: test
description: Identifies when an account hasn't signed in during the past n number of days.
references:
    - https://learn.microsoft.com/en-us/entra/id-governance/privileged-identity-management/pim-how-to-configure-security-alerts#potential-stale-accounts-in-a-privileged-role
author: <PERSON> '@markmorow', <PERSON>, '@gleeiamglo'
date: 2023-09-14
tags:
    - attack.t1078
    - attack.persistence
    - attack.privilege-escalation
logsource:
    product: azure
    service: pim
detection:
    selection:
        riskEventType: 'staleSignInAlertIncident'
    condition: selection
falsepositives:
    - Investigate if potential generic account that cannot be removed.
level: high
