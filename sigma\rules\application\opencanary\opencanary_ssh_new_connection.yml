title: OpenCanary - SSH New Connection Attempt
id: cd55f721-5623-4663-bd9b-5229cab5237d
status: test
description: Detects instances where an SSH service on an OpenCanary node has had a connection attempt.
references:
    - https://opencanary.readthedocs.io/en/latest/starting/configuration.html#services-configuration
    - https://github.com/thinkst/opencanary/blob/a0896adfcaf0328cfd5829fe10d2878c7445138e/opencanary/logger.py#L52
author: Security Onion Solutions
date: 2024-03-08
tags:
    - attack.initial-access
    - attack.lateral-movement
    - attack.persistence
    - attack.t1133
    - attack.t1021
    - attack.t1078
logsource:
    category: application
    product: opencanary
detection:
    selection:
        logtype: 4000
    condition: selection
falsepositives:
    - Unlikely
level: high
