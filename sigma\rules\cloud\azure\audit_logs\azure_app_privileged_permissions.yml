title: App Granted Privileged Delegated Or App Permissions
id: 5aecf3d5-f8a0-48e7-99be-3a759df7358f
related:
    - id: ba2a7c80-027b-460f-92e2-57d113897dbc
      type: obsolete
status: test
description: Detects when administrator grants either application permissions (app roles) or highly privileged delegated permissions
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#application-granted-highly-privileged-permissions
author: <PERSON> '@baileybercik', <PERSON> '@markmorow'
date: 2022-07-28
modified: 2023-03-29
tags:
    - attack.persistence
    - attack.privilege-escalation
    - attack.t1098.003
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message: Add app role assignment to service principal
    condition: selection
falsepositives:
    - When the permission is legitimately needed for the app
level: high
