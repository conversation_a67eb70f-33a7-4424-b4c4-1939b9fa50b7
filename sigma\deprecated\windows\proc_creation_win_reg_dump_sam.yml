title: Registry Dump of SAM Creds and Secrets
id: 038cd51c-3ad8-41c5-ba8f-5d1c92f3cc1e
related:
    - id: fd877b94-9bb5-4191-bb25-d79cbd93c167
      type: similar
status: deprecated
description: Adversaries may attempt to extract credential material from the Security Account Manager (SAM) database either through Windows Registry where the SAM database is stored
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1003.002/T1003.002.md#atomic-test-1---registry-dump-of-sam-creds-and-secrets
author: frack113
date: 2022/01/05
modified: 2023/02/04
tags:
    - attack.credential_access
    - attack.t1003.002
logsource:
    category: process_creation
    product: windows
detection:
    selection_reg:
        CommandLine|contains: ' save '
    selection_key:
        CommandLine|contains:
            - HK<PERSON>\sam
            - HKLM\system
            - HKLM\security
    condition: all of selection_*
falsepositives:
    - Unknown
level: high
