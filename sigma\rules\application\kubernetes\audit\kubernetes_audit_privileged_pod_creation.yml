title: Privileged Container Deployed
id: c5cd1b20-36bb-488d-8c05-486be3d0cb97
status: test
description: |
    Detects the creation of a "privileged" container, an action which could be indicative of a threat actor mounting a container breakout attacks.
    A privileged container is a container that can access the host with all of the root capabilities of the host machine. This allows it to view, interact and modify processes, network operations, IPC calls, the file system, mount points, SELinux configurations etc. as the root user on the host.
    Various versions of "privileged" containers can be specified, e.g. by setting the securityContext.privileged flag in the resource specification, setting non-standard Linux capabilities, or configuring the hostNetwork/hostPID fields
references:
    - https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/Privileged%20container/
    - https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_finding-types-kubernetes.html#privilegeescalation-kubernetes-privilegedcontainer
    - https://www.elastic.co/guide/en/security/current/kubernetes-pod-created-with-hostnetwork.html
    - https://www.elastic.co/guide/en/security/current/kubernetes-container-created-with-excessive-linux-capabilities.html
author: <PERSON> (@laripping)
date: 2024-03-26
tags:
    - attack.t1611
    - attack.privilege-escalation
logsource:
    category: application
    product: kubernetes
    service: audit
detection:
    selection:
        verb: 'create'
        objectRef.resource: 'pods'
        capabilities: '*' # Note: Add the "exists" when it's implemented in SigmaHQ/Aurora
    condition: selection
falsepositives:
    - Unknown
level: low
