title: Kubernetes Unauthorized or Unauthenticated Access
id: 0d933542-1f1f-420d-97d4-21b2c3c492d9
status: test
description: |
    Detects when a request to the Kubernetes API is rejected due to lack of authorization or due to an expired authentication token being used.
    This may indicate an attacker attempting to leverage credentials they have obtained.
references:
    - https://kubernetes.io/docs/reference/config-api/apiserver-audit.v1/
    - https://www.datadoghq.com/blog/monitor-kubernetes-audit-logs/#monitor-api-authentication-issues
author: kelnage
date: 2024-04-12
tags:
    - attack.privilege-escalation
logsource:
    product: kubernetes
    service: audit
detection:
    selection:
        responseStatus.code:
            - 401 # Unauthorized
            - 403 # Forbidden
    condition: selection
falsepositives:
    - A misconfigured RBAC policy, a mistake by a valid user, or a wider issue with authentication tokens can also generate these errors.
level: low
