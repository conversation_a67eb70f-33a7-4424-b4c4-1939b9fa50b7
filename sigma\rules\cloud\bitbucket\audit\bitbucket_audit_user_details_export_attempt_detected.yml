title: Bitbucket User Details Export Attempt Detected
id: 5259cbf2-0a75-48bf-b57a-c54d6fabaef3
status: test
description: Detects user data export activity.
references:
    - https://confluence.atlassian.com/bitbucketserver/audit-log-events-*********.html
    - https://support.atlassian.com/security-and-access-policies/docs/export-user-accounts
author: <PERSON> (@faisalusuf)
date: 2024-02-25
tags:
    - attack.collection
    - attack.reconnaissance
    - attack.discovery
    - attack.t1213
    - attack.t1082
    - attack.t1591.004
logsource:
    product: bitbucket
    service: audit
    definition: 'Requirements: "Advance" log level is required to receive these audit events.'
detection:
    selection:
        auditType.category: 'Users and groups'
        auditType.action:
            - 'User permissions export failed'
            - 'User permissions export started'
            - 'User permissions exported'
    condition: selection
falsepositives:
    - Legitimate user activity.
level: medium
