title: AWS New Lambda Layer Attached
id: 97fbabf8-8e1b-47a2-b7d5-a418d2b95e3d
status: test
description: |
  Detects when a user attached a Lambda layer to an existing Lambda function.
  A malicious Lambda layer could execute arbitrary code in the context of the function's IAM role.
  This would give an adversary access to resources that the function has access to.
references:
    - https://docs.aws.amazon.com/lambda/latest/dg/API_UpdateFunctionConfiguration.html
    - https://github.com/clearvector/lambda-spy
author: <PERSON>
date: 2021-09-23
modified: 2025-03-17
tags:
    - attack.privilege-escalation
logsource:
    product: aws
    service: cloudtrail
detection:
    selection:
        eventSource: lambda.amazonaws.com
        eventName|startswith: 'UpdateFunctionConfiguration'
        requestParameters.layers|contains: '*'
    condition: selection
falsepositives:
    - Lambda Layer being attached may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Lambda Layer being attached from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: low
