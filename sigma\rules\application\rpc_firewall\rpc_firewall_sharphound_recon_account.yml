title: SharpHound Recon Account Discovery
id: 65f77b1e-8e79-45bf-bb67-5988a8ce45a5
status: test
description: Detects remote RPC calls useb by SharpHound to map remote connections and local group membership.
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-wkst/55118c55-2122-4ef9-8664-0c1ff9e168f3
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-WKST.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, De<PERSON>
date: 2022-01-01
tags:
    - attack.t1087
    - attack.discovery
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:6bffd098-a112-3610-9833-46c3f87e345a opnum:2'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 6bffd098-a112-3610-9833-46c3f87e345a
        OpNum: 2
    condition: selection
falsepositives:
    - Unknown
level: high
