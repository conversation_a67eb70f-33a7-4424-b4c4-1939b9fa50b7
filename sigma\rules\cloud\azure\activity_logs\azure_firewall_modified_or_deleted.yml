title: Azure Firewall Modified or Deleted
id: 512cf937-ea9b-4332-939c-4c2c94baadcd
status: test
description: Identifies when a firewall is created, modified, or deleted.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON> @austinsonger
date: 2021-08-08
modified: 2022-08-23
tags:
    - attack.impact
    - attack.defense-evasion
    - attack.t1562.004
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.NETWORK/AZUREFIREWALLS/WRITE
            - MICROSOFT.NETWORK/AZUREFIREWALLS/DELETE
    condition: selection
falsepositives:
    - Firewall being modified or deleted may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Firewall modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
