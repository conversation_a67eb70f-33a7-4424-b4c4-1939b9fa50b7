title: Okta Suspicious Activity Reported by End-user
id: 07e97cc6-aed1-43ae-9081-b3470d2367f1
status: test
description: Detects when an Okta end-user reports activity by their account as being potentially suspicious.
references:
    - https://developer.okta.com/docs/reference/api/system-log/
    - https://github.com/okta/workflows-templates/blob/1164f0eb71ce47c9ddc7d850e9ab87b5a2b42333/workflows/suspicious_activity_reported/readme.md
author: kelnage
date: 2023-09-07
tags:
    - attack.resource-development
    - attack.t1586.003
logsource:
    product: okta
    service: okta
detection:
    selection:
        eventtype: 'user.account.report_suspicious_activity_by_enduser'
    condition: selection
falsepositives:
    - If an end-user incorrectly identifies normal activity as suspicious.
level: high
