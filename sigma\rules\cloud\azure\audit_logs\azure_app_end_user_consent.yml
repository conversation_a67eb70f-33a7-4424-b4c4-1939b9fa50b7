title: End User Consent
id: 9b2cc4c4-2ad4-416d-8e8e-ee6aa6f5035a
status: test
description: Detects when an end user consents to an application
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#end-user-consent
author: <PERSON> '@baileybercik', <PERSON> '@markmorow'
date: 2022-07-28
tags:
    - attack.credential-access
    - attack.t1528
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        ConsentContext.IsAdminConsent: 'false'
    condition: selection
falsepositives:
    - Unknown
level: low
