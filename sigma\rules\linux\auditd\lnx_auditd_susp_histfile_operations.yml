title: Suspicious History File Operations - Linux
id: eae8ce9f-bde9-47a6-8e79-f20d18419910
status: test
description: 'Detects commandline operations on shell history files'
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1552.003/T1552.003.md
author: '<PERSON>, oscd.community'
date: 2020-10-17
modified: 2022-11-28
tags:
    - attack.credential-access
    - attack.t1552.003
logsource:
    product: linux
    service: auditd
detection:
    execve:
        type: EXECVE
    history:
        - '.bash_history'
        - '.zsh_history'
        - '.zhistory'
        - '.history'
        - '.sh_history'
        - 'fish_history'
    condition: execve and history
fields:
    - a0
    - a1
    - a2
    - a3
    - key
falsepositives:
    - Legitimate administrative activity
    - Legitimate software, cleaning hist file
level: medium
