title: User Access Blocked by Azure Conditional Access
id: 9a60e676-26ac-44c3-814b-0c2a8b977adf
status: test
description: |
    Detect access has been blocked by Conditional Access policies.
    The access policy does not allow token issuance which might be sights≈ of unauthorizeed login to valid accounts.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts
author: AlertIQ
date: 2021-10-10
modified: 2022-12-25
tags:
    - attack.credential-access
    - attack.initial-access
    - attack.t1110
    - attack.t1078.004
logsource:
    product: azure
    service: signinlogs
detection:
    selection:
        ResultType: 53003
    condition: selection
falsepositives:
    - Unknown
level: medium
