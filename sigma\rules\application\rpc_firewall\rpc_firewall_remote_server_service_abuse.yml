title: Remote Server Service Abuse
id: b6ea3cc7-542f-43ef-bbe4-980fbed444c7
status: test
description: Detects remote RPC calls to possibly abuse remote encryption service via MS-SRVS
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-srvs/accf23b0-0f57-441c-9185-43041f1b0ee9
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-SRVS.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, De<PERSON>
date: 2022-01-01
tags:
    - attack.lateral-movement
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:4b324fc8-1670-01d3-1278-5a47bf6ee188'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 4b324fc8-1670-01d3-1278-5a47bf6ee188
    condition: selection
falsepositives:
    - Legitimate remote share creation
level: high
