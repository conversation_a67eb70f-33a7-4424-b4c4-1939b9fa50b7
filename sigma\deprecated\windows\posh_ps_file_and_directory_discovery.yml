title: Powershell File and Directory Discovery
id: d23f2ba5-9da0-4463-8908-8ee47f614bb9
status: deprecated
description: |
    Adversaries may enumerate files and directories or may search in specific locations of a host or network share for certain information within a file system.
    Adversaries may use the information from [File and Directory Discovery](https://attack.mitre.org/techniques/T1083) during automated discovery to shape follow-on behaviors,
    including whether or not the adversary fully infects the target and/or attempts specific actions.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1083/T1083.md
author: frack113
date: 2021/12/15
modified: 2023/12/11
tags:
    - attack.discovery
    - attack.t1083
logsource:
    product: windows
    category: ps_script
    definition: 'Requirements: Script Block Logging must be enabled'
detection:
    selection:
        ScriptBlockText|contains:
            - ls
            - get-childitem
            - gci
    recurse:
        ScriptBlockText|contains: '-recurse'
    condition: selection and recurse
falsepositives:
    - Unknown
level: low
