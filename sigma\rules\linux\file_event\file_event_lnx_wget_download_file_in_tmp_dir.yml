title: Wget Creating Files in Tmp Directory
id: 35a05c60-9012-49b6-a11f-6bab741c9f74
status: test
description: Detects the use of wget to download content in a temporary directory such as "/tmp" or "/var/tmp"
references:
    - https://blogs.jpcert.or.jp/en/2023/05/gobrat.html
    - https://jstnk9.github.io/jstnk9/research/GobRAT-Malware/
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
author: <PERSON><PERSON><PERSON>, @Joseliyo_Jstnk
date: 2023-06-02
tags:
    - attack.command-and-control
    - attack.t1105
logsource:
    product: linux
    category: file_event
detection:
    selection:
        Image|endswith: '/wget'
        TargetFilename|startswith:
            - '/tmp/'
            - '/var/tmp/'
    condition: selection
falsepositives:
    - Legitimate downloads of files in the tmp folder.
level: medium
