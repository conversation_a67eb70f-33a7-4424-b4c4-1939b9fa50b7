title: Credential Dumping by Pypykatz
id: 7186e989-4ed7-4f4e-a656-4674b9e3e48b
status: test
description: Detects LSASS process access by pypykatz for credential dumping.
references:
    - https://github.com/skelsec/pypykatz
author: <PERSON><PERSON><PERSON>
date: 2021/08/03
modified: 2022/10/09
tags:
    - attack.credential_access
    - attack.t1003.001
logsource:
    category: process_access
    product: windows
detection:
    selection:
        TargetImage|endswith: '\lsass.exe'
        CallTrace|contains|all:
            - 'C:\Windows\SYSTEM32\ntdll.dll+'
            - 'C:\Windows\System32\KERNELBASE.dll+'
            - 'libffi-7.dll'
            - '_ctypes.pyd+'
            - 'python3*.dll+'   # Pypy requires python>=3.6
        GrantedAccess: '0x1FFFFF'
    condition: selection
falsepositives:
    - Unknown
level: critical
