title: Shell Invocation Via Ssh - Linux
id: 8737b7f6-8df3-4bb7-b1da-06019b99b687
status: test
description: |
    Detects the use of the "ssh" utility to execute a shell. Such behavior may be associated with privilege escalation, unauthorized command execution, or to break out from restricted environments.
references:
    - https://gtfobins.github.io/gtfobins/ssh/
    - https://www.elastic.co/guide/en/security/current/linux-restricted-shell-breakout-via-linux-binary-s.html
author: <PERSON>, <PERSON>, <PERSON>, <PERSON> (Bloomberg L.P.)
date: 2024-08-29
tags:
    - attack.execution
    - attack.t1059
logsource:
    category: process_creation
    product: linux
detection:
    selection_img:
        Image|endswith: '/ssh'
        CommandLine|contains:
            - 'ProxyCommand=;'
            - 'permitlocalcommand=yes'
            - 'localhost'
    selection_cli:
        CommandLine|contains:
            - '/bin/bash'
            - '/bin/dash'
            - '/bin/fish'
            - '/bin/sh'
            - '/bin/zsh'
            - 'sh 0<&2 1>&2'
            - 'sh 1>&2 0<&2'
    condition: all of selection_*
falsepositives:
    - Unknown
level: high
