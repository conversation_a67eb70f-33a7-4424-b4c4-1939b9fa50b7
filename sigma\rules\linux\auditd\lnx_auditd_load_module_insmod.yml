title: Loading of Kernel Module via Insmod
id: 106d7cbd-80ff-4985-b682-a7043e5acb72
status: test
description: |
    Detects loading of kernel modules with insmod command.
    Loadable Kernel Modules (LKMs) are pieces of code that can be loaded and unloaded into the kernel upon demand.
    Adversaries may use LKMs to obtain persistence within the system or elevate the privileges.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1547.006/T1547.006.md
    - https://linux.die.net/man/8/insmod
    - https://man7.org/linux/man-pages/man8/kmod.8.html
author: 'Pawel Mazur'
date: 2021-11-02
modified: 2022-12-25
tags:
    - attack.persistence
    - attack.privilege-escalation
    - attack.t1547.006
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'SYSCALL'
        comm: insmod
        exe: /usr/bin/kmod
    condition: selection
falsepositives:
    - Unknown
level: high
