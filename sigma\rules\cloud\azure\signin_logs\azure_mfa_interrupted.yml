title: Multifactor Authentication Interrupted
id: 5496ff55-42ec-4369-81cb-00f417029e25
status: test
description: Identifies user login with multifactor authentication failures, which might be an indication an attacker has the password for the account but can't pass the MFA challenge.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts
author: AlertIQ
date: 2021-10-10
modified: 2022-12-18
tags:
    - attack.initial-access
    - attack.credential-access
    - attack.t1078.004
    - attack.t1110
    - attack.t1621
logsource:
    product: azure
    service: signinlogs
detection:
    selection_50074:
        ResultType: 50074
        ResultDescription|contains: 'Strong Auth required'
    selection_500121:
        ResultType: 500121
        ResultDescription|contains: 'Authentication failed during strong authentication request'
    condition: 1 of selection_*
falsepositives:
    - Unknown
level: medium
