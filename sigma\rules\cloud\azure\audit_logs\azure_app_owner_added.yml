title: Added Owner To Application
id: ********-9fc4-460e-a92e-511aa60baec1
status: test
description: Detects when a new owner is added to an application. This gives that account privileges to make modifications and configuration changes to the application.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#new-owner
author: <PERSON> '@markmorow', <PERSON> '@baileybercik'
date: 2022-06-02
tags:
    - attack.t1552
    - attack.credential-access
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message: Add owner to application
    condition: selection
falsepositives:
    - When a new application owner is added by an administrator
level: medium
