title: Indirect Command Execution
id: fa47597e-90e9-41cd-ab72-c3b74cfb0d02
status: deprecated
description: Detect indirect command execution via Program Compatibility Assistant (pcalua.exe or forfiles.exe).
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1202/T1202.md
    - https://eqllib.readthedocs.io/en/latest/analytics/884a7ccd-7305-4130-82d0-d4f90bc118b6.html
author: <PERSON><PERSON><PERSON><PERSON> (originally from Atomic Blue Detections, Endgame), oscd.community
date: 2019/10/24
modified: 2023/01/04
tags:
    - attack.defense_evasion
    - attack.t1202
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        ParentImage|endswith:
            - '\pcalua.exe'
            - '\forfiles.exe'
    condition: selection
fields:
    - ComputerName
    - User
    - ParentCommandLine
    - CommandLine
falsepositives:
    - Need to use extra processing with 'unique_count' / 'filter' to focus on outliers as opposed to commonly seen artifacts.
    - Legitimate usage of scripts.
level: low
