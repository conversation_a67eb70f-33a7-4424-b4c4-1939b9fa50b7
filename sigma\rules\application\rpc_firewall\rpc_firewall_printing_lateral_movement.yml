title: Remote Printing Abuse for Lateral Movement
id: bc3a4b0c-e167-48e1-aa88-b3020950e560
status: test
description: Detects remote RPC calls to possibly abuse remote printing service via MS-RPRN / MS-PAR
references:
    - https://msrc.microsoft.com/update-guide/vulnerability/CVE-2021-34527
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-rprn/d42db7d5-f141-4466-8f47-0a4be14e2fc1
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-pan/e44d984c-07d3-414c-8ffc-f8c8ad8512a8
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-RPRN-PAR.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, <PERSON><PERSON>
date: 2022-01-01
tags:
    - attack.lateral-movement
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:12345678-1234-abcd-ef00-0123456789ab or 76f03f96-cdfd-44fc-a22c-64950a001209 or ae33069b-a2a8-46ee-a235-ddfd339be281 or 0b6edbfa-4a24-4fc6-8a23-942b1eca65d1'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid:
            - 12345678-1234-abcd-ef00-0123456789ab
            - 76f03f96-cdfd-44fc-a22c-64950a001209
            - 0b6edbfa-4a24-4fc6-8a23-942b1eca65d1
            - ae33069b-a2a8-46ee-a235-ddfd339be281
    condition: selection
falsepositives:
    - Actual printing
level: high
