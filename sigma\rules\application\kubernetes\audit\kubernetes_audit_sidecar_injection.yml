title: Potential Sidecar Injection Into Running Deployment
id: ad9012a6-e518-4432-9890-f3b82b8fc71f
status: test
description: |
    Detects attempts to inject a sidecar container into a running deployment.
    A sidecar container is an additional container within a pod, that resides alongside the main container.
    One way to add containers to running resources like Deployments/DeamonSets/StatefulSets, is via a "kubectl patch" operation.
    By injecting a new container within a legitimate pod, an attacker can run their code and hide their activity, instead of running their own separated pod in the cluster.
references:
    - https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch
    - https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/Sidecar%20Injection/
author: <PERSON> (@laripping)
date: 2024-03-26
tags:
    - attack.t1609
    - attack.execution
logsource:
    category: application
    product: kubernetes
    service: audit
detection:
    selection:
        verb: 'patch'
        apiGroup: 'apps'
        objectRef.resource: 'deployments'
    condition: selection
falsepositives:
    - Unknown
level: medium
