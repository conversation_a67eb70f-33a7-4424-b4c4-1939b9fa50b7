title: SharpHound Recon Sessions
id: 6d580420-ff3f-4e0e-b6b0-41b90c787e28
status: test
description: Detects remote RPC calls useb by SharpHound to map remote connections and local group membership.
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-srvs/02b1f559-fda2-4ba3-94c2-806eb2777183
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-SRVS.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, <PERSON><PERSON>
date: 2022-01-01
tags:
    - attack.discovery
    - attack.t1033
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:4b324fc8-1670-01d3-1278-5a47bf6ee188 opnum:12'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 4b324fc8-1670-01d3-1278-5a47bf6ee188
        OpNum: 12
    condition: selection
falsepositives:
    - Unknown
level: high
