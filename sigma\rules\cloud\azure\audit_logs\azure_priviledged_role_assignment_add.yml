title: User Added To Privilege Role
id: 49a268a4-72f4-4e38-8a7b-885be690c5b5
status: test
description: Detects when a user is added to a privileged role.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-identity-management#azure-ad-roles-assignment
author: <PERSON> '@markmorow', <PERSON><PERSON><PERSON>, '@Yochana-H'
date: 2022-08-06
tags:
    - attack.privilege-escalation
    - attack.defense-evasion
    - attack.t1078.004
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message:
            - Add eligible member (permanent)
            - Add eligible member (eligible)
    condition: selection
falsepositives:
    - Legtimate administrator actions of adding members from a role
level: high
