title: Binary Padding - Linux
id: c52a914f-3d8b-4b2a-bb75-b3991e75f8ba
status: test
description: |
    Adversaries may use binary padding to add junk data and change the on-disk representation of malware.
    This rule detect using dd and truncate to add a junk data to file.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1027.001/T1027.001.md
author: <PERSON>, oscd.community
date: 2020-10-13
modified: 2023-05-03
tags:
    - attack.defense-evasion
    - attack.t1027.001
logsource:
    product: linux
    service: auditd
detection:
    selection_execve:
        type: 'EXECVE'
    keywords_truncate:
        '|all':
            - 'truncate'
            - '-s'
    keywords_dd:
        '|all':
            - 'dd'
            - 'if='
    keywords_filter:
        - 'of='
    condition: selection_execve and (keywords_truncate or (keywords_dd and not keywords_filter))
falsepositives:
    - Unknown
level: high
