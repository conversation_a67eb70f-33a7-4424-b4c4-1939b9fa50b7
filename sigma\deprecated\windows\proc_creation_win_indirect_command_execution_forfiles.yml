title: Indirect Command Exectuion via Forfiles
id: a85cf4e3-56ee-4e79-adeb-789f8fb209a8
related:
    - id: fa47597e-90e9-41cd-ab72-c3b74cfb0d02
      type: obsolete
status: deprecated
description: Detects execition of commands and binaries from the context of "forfiles.exe". This can be used as a LOLBIN in order to bypass application whitelisting.
references:
    - https://github.com/elastic/protections-artifacts/commit/746086721fd385d9f5c6647cada1788db4aea95f#diff-73d61931b2c77fde294189ce5d62323b416296a7c23ea98a608f425566538d1a
    - https://lolbas-project.github.io/lolbas/Binaries/Forfiles/
author: <PERSON> (rule), <PERSON><PERSON> (idea), <PERSON><PERSON><PERSON><PERSON> (originally from Atomic Blue Detections, Endgame), oscd.community
date: 2022/10/17
modified: 2023/01/04
tags:
    - attack.defense_evasion
    - attack.t1202
logsource:
    product: windows
    category: process_creation
detection:
    selection_parent:
        ParentImage|endswith: '\forfiles.exe'
    selection_c:
        ParentCommandLine|contains:
            - ' /c '
            - ' -c '
    selection_p:
        ParentCommandLine|contains:
            - ' /p '
            - ' -p '
    selection_m:
        ParentCommandLine|contains:
            - ' /m '
            - ' -m '
    filter:
        Image|endswith: '\cmd.exe'
        CommandLine|contains|all:
            - 'xcopy'
            - 'cmd /c del'
    condition: all of selection_* and not filter
falsepositives:
    - Unknown
level: medium
