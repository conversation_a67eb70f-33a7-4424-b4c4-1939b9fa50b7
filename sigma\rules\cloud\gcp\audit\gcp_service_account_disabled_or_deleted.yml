title: Google Cloud Service Account Disabled or Deleted
id: 13f81a90-a69c-4fab-8f07-b5bb55416a9f
status: test
description: Identifies when a service account is disabled or deleted in Google Cloud.
references:
    - https://cloud.google.com/iam/docs/reference/rest/v1/projects.serviceAccounts
author: <PERSON> @austinsonger
date: 2021-08-14
modified: 2022-10-09
tags:
    - attack.impact
    - attack.t1531
logsource:
    product: gcp
    service: gcp.audit
detection:
    selection:
        gcp.audit.method_name|endswith:
            - .serviceAccounts.disable
            - .serviceAccounts.delete
    condition: selection
falsepositives:
    - Service Account being disabled or deleted may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Service Account disabled or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
