title: AWS Route 53 Domain Transferred to Another Account
id: b056de1a-6e6e-4e40-a67e-97c9808cf41b
status: test
description: Detects when a request has been made to transfer a Route 53 domain to another AWS account.
references:
    - https://github.com/elastic/detection-rules/blob/c76a39796972ecde44cb1da6df47f1b6562c9770/rules/integrations/aws/persistence_route_53_domain_transferred_to_another_account.toml
author: El<PERSON>, <PERSON> @austinsonger
date: 2021-07-22
modified: 2022-10-09
tags:
    - attack.persistence
    - attack.credential-access
    - attack.t1098
logsource:
    product: aws
    service: cloudtrail
detection:
    selection:
        eventSource: route53.amazonaws.com
        eventName: TransferDomainToAnotherAwsAccount
    condition: selection
falsepositives:
    - A domain may be transferred to another AWS account by a system or network administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment. Domain transfers from unfamiliar users or hosts should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: low
