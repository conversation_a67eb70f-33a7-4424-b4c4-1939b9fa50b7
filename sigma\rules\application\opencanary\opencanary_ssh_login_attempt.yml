title: OpenCanary - SSH Login Attempt
id: ff7139bc-fdb1-4437-92f2-6afefe8884cb
status: test
description: Detects instances where an SSH service on an OpenCanary node has had a login attempt.
references:
    - https://opencanary.readthedocs.io/en/latest/starting/configuration.html#services-configuration
    - https://github.com/thinkst/opencanary/blob/a0896adfcaf0328cfd5829fe10d2878c7445138e/opencanary/logger.py#L52
author: Security Onion Solutions
date: 2024-03-08
tags:
    - attack.initial-access
    - attack.lateral-movement
    - attack.persistence
    - attack.t1133
    - attack.t1021
    - attack.t1078
logsource:
    category: application
    product: opencanary
detection:
    selection:
        logtype: 4002
    condition: selection
falsepositives:
    - Unlikely
level: high
