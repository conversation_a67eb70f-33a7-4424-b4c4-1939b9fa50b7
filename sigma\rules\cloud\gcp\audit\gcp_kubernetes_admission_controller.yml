title: Google Cloud Kubernetes Admission Controller
id: 6ad91e31-53df-4826-bd27-0166171c8040
status: test
description: |
  Identifies when an admission controller is executed in GCP Kubernetes.
  A Kubernetes Admission controller intercepts, and possibly modifies, requests to the Kubernetes API server.
  The behavior of this admission controller is determined by an admission webhook (MutatingAdmissionWebhook or ValidatingAdmissionWebhook) that the user deploys in the cluster.
  An adversary can use such webhooks as the MutatingAdmissionWebhook for obtaining persistence in the cluster.
  For example, attackers can intercept and modify the pod creation operations in the cluster and add their malicious container to every created pod. An adversary can use the webhook ValidatingAdmissionWebhook, which could be used to obtain access credentials.
  An adversary could use the webhook to intercept the requests to the API server, record secrets, and other sensitive information.
references:
    - https://cloud.google.com/kubernetes-engine/docs
author: <PERSON> @austinsonger
date: 2021-11-25
modified: 2022-12-18
tags:
    - attack.persistence
    - attack.t1078
    - attack.credential-access
    - attack.t1552
    - attack.t1552.007
logsource:
    product: gcp
    service: gcp.audit
detection:
    selection:
        gcp.audit.method_name|startswith: 'admissionregistration.k8s.io.v'
        gcp.audit.method_name|contains:
            - '.mutatingwebhookconfigurations.'
            - '.validatingwebhookconfigurations.'
        gcp.audit.method_name|endswith:
            - 'create'
            - 'patch'
            - 'replace'
    condition: selection
falsepositives:
    - Google Cloud Kubernetes Admission Controller may be done by a system administrator.
    - If known behavior is causing false positives, it can be exempted from the rule.
level: medium
