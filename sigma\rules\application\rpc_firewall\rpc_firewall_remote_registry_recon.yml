title: Remote Registry Recon
id: d8ffe17e-04be-4886-beb9-c1dd1944b9a8
status: test
description: Detects remote RPC calls to collect information
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-rrp/0fa3191d-bb79-490a-81bd-54c2601b7a78
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-RRP.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, Dekel <PERSON>
date: 2022-01-01
tags:
    - attack.discovery
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:338cd001-2244-31f1-aaaa-************"'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 338cd001-2244-31f1-aaaa-************
    filter:
        OpNum:
            - 6
            - 7
            - 8
            - 13
            - 18
            - 19
            - 21
            - 22
            - 23
            - 35
    condition: selection and not filter
falsepositives:
    - Remote administration of registry values
level: high
