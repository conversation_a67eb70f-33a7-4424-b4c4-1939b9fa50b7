title: Potential RCE Exploitation Attempt In NodeJS
id: 97661d9d-2beb-4630-b423-68985291a8af
status: test
description: Detects process execution related errors in NodeJS. If the exceptions are caused due to user input then they may suggest an RCE vulnerability.
references:
    - https://www.wix.engineering/post/threat-and-vulnerability-hunting-with-application-server-error-logs
author: <PERSON><PERSON>
date: 2023-02-11
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: nodejs
    definition: 'Requirements: application error logs must be collected (with LOG_LEVEL=ERROR and above)'
detection:
    keywords:
        - 'node:child_process'
    condition: keywords
falsepositives:
    - Puppeteer invocation exceptions often contain child_process related errors, that doesn't necessarily mean that the app is vulnerable.
level: high
