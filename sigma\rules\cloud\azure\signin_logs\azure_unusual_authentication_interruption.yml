title: Azure Unusual Authentication Interruption
id: 8366030e-7216-476b-9927-271d79f13cf3
status: test
description: Detects when there is a interruption in the authentication process.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts
author: <PERSON> @austinsonger
date: 2021-11-26
modified: 2022-12-18
tags:
    - attack.initial-access
    - attack.t1078
logsource:
    product: azure
    service: signinlogs
detection:
    selection_50097:
        ResultType: 50097
        ResultDescription: 'Device authentication is required'
    selection_50155:
        ResultType: 50155
        ResultDescription: 'DeviceAuthenticationFailed'
    selection_50158:
        ResultType: 50158
        ResultDescription: 'ExternalSecurityChallenge - External security challenge was not satisfied'
    condition: 1 of selection_*
falsepositives:
    - Unknown
level: medium
