name: Validate Sigma rules

on:
  push:
    branches:
      - "*"
    paths:
      - "deprecated/**.yml"
      - "rules-compliance/**.yml"
      - "rules-dfir/**.yml"
      - "rules-emerging-threats/**.yml"
      - "rules-placeholder/**.yml"
      - "rules-threat-hunting/**.yml"
      - "rules/**.yml"
      - "tests/validate-sigma-schema/validate.sh"
      - "unsupported/**.yml"
  pull_request:
    branches:
      - master
    paths:
      - "deprecated/**.yml"
      - "rules-compliance/**.yml"
      - "rules-dfir/**.yml"
      - "rules-emerging-threats/**.yml"
      - "rules-placeholder/**.yml"
      - "rules-threat-hunting/**.yml"
      - "rules/**.yml"
      - "tests/validate-sigma-schema/validate.sh"
      - "unsupported/**.yml"

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  sigma-rules-validator:
    runs-on: ubuntu-latest
    steps:
      - name: Validate Sigma rules
        uses: SigmaHQ/sigma-rules-validator@v1
        with:
          paths: |-
            ./rules
            ./rules-compliance
            ./rules-dfir
            ./rules-emerging-threats
            ./rules-placeholder
            ./rules-threat-hunting
          schemaFile: ${{ github.workspace }}/tests/validate-sigma-schema/sigma-schema.json
