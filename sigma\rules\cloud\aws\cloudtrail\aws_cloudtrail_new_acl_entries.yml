title: New Network ACL Entry Added
id: e1f7febb-7b94-4234-b5c6-00fb8500f5dd
status: test
description: |
    Detects that network ACL entries have been added to a route table which could indicate that new attack vectors have been opened up in the AWS account.
references:
    - https://www.gorillastack.com/blog/real-time-events/important-aws-cloudtrail-security-events-tracking/
author: jamesc-grafana
date: 2024-07-11
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    product: aws
    service: cloudtrail
detection:
    selection:
        eventSource: 'ec2.amazonaws.com'
        eventName: 'CreateNetworkAclEntry'
    condition: selection
falsepositives:
    - Legitimate use of ACLs to enable customer and staff access from the public internet into a public VPC
level: low
