title: Rclone Execution via Command Line or PowerShell
id: cb7286ba-f207-44ab-b9e6-760d82b84253
description: Detects Rclone which is commonly used by ransomware groups for exfiltration
status: deprecated
date: 2021/05/26
modified: 2022/04/11
author: <PERSON> (@beardofbinary) - NCC Group
references:
    - https://research.nccgroup.com/2021/05/27/detecting-rclone-an-effective-tool-for-exfiltration/
tags:
    - attack.exfiltration
    - attack.t1567.002
falsepositives:
    - Legitimate Rclone usage (rare)
level: high
logsource:
    product: windows
    category: process_creation
detection:
    exec_selection:
        Image|endswith: '\rclone.exe'
        ParentImage|endswith:
            - '\PowerShell.exe'
            - '\cmd.exe'
    command_selection:
        CommandLine|contains:
            - ' pass '
            - ' user '
            - ' copy '
            - ' mega '
            - ' sync '
            - ' config '
            - ' lsd '
            - ' remote '
            - ' ls '
    description_selection:
      Description: 'Rsync for cloud storage'
    condition: command_selection and ( description_selection or exec_selection )
