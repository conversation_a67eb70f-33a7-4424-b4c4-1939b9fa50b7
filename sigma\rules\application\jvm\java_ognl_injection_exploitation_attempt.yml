title: Potential OGNL Injection Exploitation In JVM Based Application
id: 4d0af518-828e-4a04-a751-a7d03f3046ad
status: test
description: |
    Detects potential OGNL Injection exploitation, which may lead to RCE.
    OGNL is an expression language that is supported in many JVM based systems.
    OGNL Injection is the reason for some high profile RCE's such as Apache Struts (CVE-2017-5638) and Confluence (CVE-2022-26134)
references:
    - https://www.wix.engineering/post/threat-and-vulnerability-hunting-with-application-server-error-logs
author: <PERSON><PERSON>
date: 2023-02-11
tags:
    - attack.initial-access
    - attack.t1190
    - cve.2017-5638
    - cve.2022-26134
logsource:
    category: application
    product: jvm
    definition: 'Requirements: application error logs must be collected (with LOG_LEVEL=ERROR and above)'
detection:
    keywords:
        - 'org.apache.commons.ognl.OgnlException'
        - 'ExpressionSyntaxException'
    condition: keywords
falsepositives:
    - Application bugs
level: high
