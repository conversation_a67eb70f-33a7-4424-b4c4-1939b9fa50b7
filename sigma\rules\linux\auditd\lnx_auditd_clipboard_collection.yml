title: Clipboard Collection with <PERSON><PERSON><PERSON> Tool - Auditd
id: 214e7e6c-f21b-47ff-bb6f-551b2d143fcf
status: test
description: |
  Detects attempts to collect data stored in the clipboard from users with the usage of xclip tool.
  Xclip has to be installed.
  Highly recommended using rule on servers, due to high usage of clipboard utilities on user workstations.
references:
    - https://linux.die.net/man/1/xclip
    - https://www.cyberciti.biz/faq/xclip-linux-insert-files-command-output-intoclipboard/
author: 'Pawe<PERSON>'
date: 2021-09-24
modified: 2022-11-26
tags:
    - attack.collection
    - attack.t1115
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: EXECVE
        a0: xclip
        a1:
            - '-selection'
            - '-sel'
        a2:
            - clipboard
            - clip
        a3: '-o'
    condition: selection
falsepositives:
    - Legitimate usage of xclip tools
level: low
