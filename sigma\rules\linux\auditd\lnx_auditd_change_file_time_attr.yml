title: File Time Attribute Change - Linux
id: b3cec4e7-6901-4b0d-a02d-8ab2d8eb818b
status: test
description: Detect file time attribute change to hide new or changes to existing files.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1070.006/T1070.006.md
author: '<PERSON>, oscd.community'
date: 2020-10-15
modified: 2022-11-28
tags:
    - attack.defense-evasion
    - attack.t1070.006
logsource:
    product: linux
    service: auditd
detection:
    execve:
        type: 'EXECVE'
    touch:
        - 'touch'
    selection2:
        - '-t'
        - '-acmr'
        - '-d'
        - '-r'
    condition: execve and touch and selection2
falsepositives:
    - Unknown
level: medium
