name: "False Positive Report"
description: Report false positives with SIGMA rules
labels: [False-Positive]
assignees:
  - nasbench
body:
- type: input
  attributes:
    label: Rule UUID
    placeholder: "f3be1b1d-eb3c-4ab1-b5e5-81e330fa2cd0"
    description: |
      You can copy the rule id from the `id` field in the rule.
  validations:
    required: true

- type: textarea
  attributes:
    label: Example EventLog
    description: An event log example of the false positive in question
    placeholder: |
      SubjectLogonId 0x1d3f2a 
      NewProcessId 0x5f14 
      NewProcessName C:\Windows\System32\dllhost.exe 
      TokenElevationType %%1937 
      ProcessId 0x1270 
      CommandLine dllhost 
      TargetUserSid S-1-0-0 
      TargetUserName - 
      TargetDomainName - 
      TargetLogonId 0x0 
      ParentProcessName C:\Windows\System32\cmd.exe 
  validations:
    required: true

- type: textarea
  attributes:
    label: Description
    placeholder: This is just a placeholder description
    description: |
      Provide any additional information that you might think is helpful
  validations:
    required: true