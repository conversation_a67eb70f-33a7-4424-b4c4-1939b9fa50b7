title: Linux Capabilities Discovery
id: fe10751f-1995-40a5-aaa2-c97ccb4123fe
status: test
description: Detects attempts to discover the files with setuid/setgid capability on them. That would allow adversary to escalate their privileges.
references:
    - https://man7.org/linux/man-pages/man8/getcap.8.html
    - https://www.hackingarticles.in/linux-privilege-escalation-using-capabilities/
    - https://mn3m.info/posts/suid-vs-capabilities/
    - https://int0x33.medium.com/day-44-linux-capabilities-privilege-escalation-via-openssl-with-selinux-enabled-and-enforced-74d2bec02099
author: 'Pawel Mazur'
date: 2021-11-28
modified: 2022-12-25
tags:
    - attack.collection
    - attack.privilege-escalation
    - attack.t1123
    - attack.t1548
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: EXECVE
        a0: getcap
        a1: '-r'
        a2: '/'
    condition: selection
falsepositives:
    - Unknown
level: low
