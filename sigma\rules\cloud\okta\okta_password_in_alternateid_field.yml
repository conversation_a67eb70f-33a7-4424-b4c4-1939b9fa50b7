title: Potential Okta Password in AlternateID Field
id: 91b76b84-8589-47aa-9605-c837583b82a9
status: test
description: |
    Detects when a user has potentially entered their password into the
    username field, which will cause the password to be retained in log files.
references:
    - https://developer.okta.com/docs/reference/api/system-log/
    - https://www.mitiga.io/blog/how-okta-passwords-can-be-compromised-uncovering-a-risk-to-user-data
    - https://help.okta.com/en-us/Content/Topics/users-groups-profiles/usgp-create-character-restriction.htm
author: kelnage
date: 2023-04-03
modified: 2023-10-25
tags:
    - attack.credential-access
    - attack.t1552
logsource:
    product: okta
    service: okta
detection:
    selection:
        legacyeventtype: 'core.user_auth.login_failed'
    filter_main:
        # Okta service account names start with 0oa
        # Email addresses are the default format for Okta usernames, so attempt
        # to exclude alternateIds that look like valid emails
        # If your Okta configuration uses different character restrictions, you
        # will need to update this regular expression to reflect that or disable the rule for your environment
        # Possible false negatives are failed login attempts with a password that looks like a valid email address
        actor.alternateid|re: '(^0oa.*|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,10})'
    condition: selection and not filter_main
falsepositives:
    - Unlikely
level: high
