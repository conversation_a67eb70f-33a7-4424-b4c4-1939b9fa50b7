name: "Reference Archiver"

on:
  #push:
  #  branches:
  #      - "*"
  schedule:
    - cron: "30 1 1,15 * *" # At 01:30 on day-of-month 1 and 15.
  
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  archive:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
    - name: Set up Python 3.11
      uses: actions/setup-python@v4.5.0
      with:
        python-version: 3.11
    - name: Execute Reference Archiver
      run: |
        pip install PyYAML argparse requests
        python tests/reference-archiver.py
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        reviewers: nasbench, frack113, phantinuss
        delete-branch: true
        branch: 'create-pull-request/reference-archiver'
        commit-message: 'chore: archive new rule references and update cache file'
        title: 'Archive New Rule References'
        body: |
          ### Summary of the Pull Request

          This PR update the cache file used to save already archived references with newly archived results

          ### Changelog

          chore: archive new rule references and update cache file

          ### Example Log Event

          N/A

          ### Fixed Issues

          N/A

          ### SigmaHQ Rule Creation Conventions
          
          - If your PR adds new rules, please consider following and applying these [conventions](https://github.com/SigmaHQ/sigma-specification/blob/main/sigmahq/sigmahq_conventions.md)
