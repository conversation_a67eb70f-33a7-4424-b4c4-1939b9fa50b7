title: Outdated Dependency Or Vulnerability Alert Disabled
id: 34e1c7d4-0cd5-419d-9f1b-1dad3f61018d
status: test
description: |
    Dependabot performs a scan to detect insecure dependencies, and sends Dependabot alerts.
    This rule detects when an organization owner disables Dependabot alerts private repositories or Dependabot security updates for all repositories.
author: <PERSON> (@faisalusuf)
date: 2023-01-27
references:
    - https://docs.github.com/en/code-security/dependabot/dependabot-alerts/about-dependabot-alerts
    - https://docs.github.com/en/organizations/keeping-your-organization-secure/managing-security-settings-for-your-organization/managing-security-and-analysis-settings-for-your-organization
tags:
    - attack.initial-access
    - attack.t1195.001
logsource:
    product: github
    service: audit
    definition: 'Requirements: The audit log streaming feature must be enabled to be able to receive such logs. You can enable following the documentation here: https://docs.github.com/en/enterprise-cloud@latest/admin/monitoring-activity-in-your-enterprise/reviewing-audit-logs-for-your-enterprise/streaming-the-audit-log-for-your-enterprise#setting-up-audit-log-streaming'
detection:
    selection:
        action:
            - 'dependabot_alerts_new_repos.disable'
            - 'dependabot_alerts.disable'
            - 'dependabot_security_updates_new_repos.disable'
            - 'dependabot_security_updates.disable'
            - 'repository_vulnerability_alerts.disable'
    condition: selection
falsepositives:
    - Approved changes by the Organization owner. Please validate the 'actor' if authorized to make the changes.
level: high
