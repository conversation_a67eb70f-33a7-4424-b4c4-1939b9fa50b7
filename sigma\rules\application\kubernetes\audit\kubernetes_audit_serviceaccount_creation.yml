title: New Kubernetes Service Account Created
id: e31bae15-83ed-473e-bf31-faf4f8a17d36
related:
    - id: 12d027c3-b48c-4d9d-8bb6-a732200034b2
      type: derived
status: test
description: |
    Detects creation of new Kubernetes service account, which could indicate an attacker's attempt to persist within a cluster.
references:
    - https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/container%20service%20account/
author: <PERSON> (@laripping)
date: 2024-03-26
tags:
    - attack.persistence
    - attack.t1136
logsource:
    category: application
    product: kubernetes
    service: audit
detection:
    selection:
        verb: 'create'
        objectRef.resource: 'serviceaccounts'
    condition: selection
falsepositives:
    - Unknown
level: low
