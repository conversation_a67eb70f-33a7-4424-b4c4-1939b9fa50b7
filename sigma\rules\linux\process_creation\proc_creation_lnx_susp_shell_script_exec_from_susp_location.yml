title: Execution Of Script Located In Potentially Suspicious Directory
id: 30bcce26-51c5-49f2-99c8-7b59e3af36c7
status: test
description: Detects executions of scripts located in potentially suspicious locations such as "/tmp" via a shell such as "bash", "sh", etc.
references:
    - https://blogs.jpcert.or.jp/en/2023/05/gobrat.html
    - https://jstnk9.github.io/jstnk9/research/GobRAT-Malware/
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
author: <PERSON><PERSON><PERSON>, @Joseliyo_Jstnk
date: 2023-06-02
tags:
    - attack.execution
logsource:
    product: linux
    category: process_creation
detection:
    selection_img:
        Image|endswith:
            - '/bash'
            - '/csh'
            - '/dash'
            - '/fish'
            - '/ksh'
            - '/sh'
            - '/zsh'
    selection_flag:
        CommandLine|contains: ' -c '
    selection_paths:
        # Note: Add more suspicious paths
        CommandLine|contains: '/tmp/'
    condition: all of selection_*
falsepositives:
    - Unknown
level: medium
