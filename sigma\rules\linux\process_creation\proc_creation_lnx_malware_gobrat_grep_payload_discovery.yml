title: Potential GobRAT File Discovery Via Grep
id: e34cfa0c-0a50-4210-9cb3-5632d08eb041
status: test
description: Detects the use of grep to discover specific files created by the GobRAT malware
references:
    - https://blogs.jpcert.or.jp/en/2023/05/gobrat.html
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
author: <PERSON><PERSON><PERSON>, @Joseliyo_Jstnk
date: 2023-06-02
tags:
    - attack.discovery
    - attack.t1082
logsource:
    category: process_creation
    product: linux
detection:
    selection:
        Image|endswith: '/grep'
        CommandLine|contains:
            - 'apached'
            - 'frpc'
            - 'sshd.sh'
            - 'zone.arm'
    condition: selection
falsepositives:
    - Unknown
level: high
