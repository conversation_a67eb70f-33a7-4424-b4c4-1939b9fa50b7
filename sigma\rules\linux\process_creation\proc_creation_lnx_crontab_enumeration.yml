title: Crontab Enumeration
id: 403ed92c-b7ec-4edd-9947-5b535ee12d46
status: test
description: Detects usage of crontab to list the tasks of the user
references:
    - https://blogs.jpcert.or.jp/en/2023/05/gobrat.html
    - https://jstnk9.github.io/jstnk9/research/GobRAT-Malware/
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
author: <PERSON><PERSON><PERSON>, @Joseliyo_Jstnk
date: 2023-06-02
tags:
    - attack.discovery
    - attack.t1007
logsource:
    product: linux
    category: process_creation
detection:
    selection:
        Image|endswith: '/crontab'
        CommandLine|contains: ' -l'
    condition: selection
falsepositives:
    - Legitimate use of crontab
level: low
