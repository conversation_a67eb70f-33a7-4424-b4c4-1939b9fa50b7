title: Potential Server Side Template Injection In Velocity
id: 16c86189-b556-4ee8-b4c7-7e350a195a4f
status: test
description: Detects exceptions in velocity template renderer, this most likely happens due to dynamic rendering of user input and may lead to RCE.
references:
    - https://antgarsil.github.io/posts/velocity/
    - https://www.wix.engineering/post/threat-and-vulnerability-hunting-with-application-server-error-logs
author: <PERSON><PERSON>
date: 2023-02-11
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: velocity
    definition: 'Requirements: application error logs must be collected (with LOG_LEVEL=ERROR and above)'
detection:
    keywords:
        - 'ParseErrorException'
        - 'VelocityException'
        - 'TemplateInitException'
    condition: keywords
falsepositives:
    - Application bugs
    - Missing .vm files
level: high
