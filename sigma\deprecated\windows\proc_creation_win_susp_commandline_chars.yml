title: Suspicious Characters in CommandLine
id: 2c0d2d7b-30d6-4d14-9751-7b9113042ab9
status: deprecated
description: Detects suspicious Unicode characters in the command line, which could be a sign of obfuscation or defense evasion
references:
    - https://www.wietzebeukema.nl/blog/windows-command-line-obfuscation
author: <PERSON><PERSON><PERSON> (Nextron Systems)
date: 2022/04/27
modified: 2023/03/03
tags:
    - attack.defense_evasion
logsource:
    product: windows
    category: process_creation
detection:
    selection_spacing_modifiers:
        CommandLine|contains: # spacing modifier letters that get auto-replaced
            - 'ˣ' # 0x02E3
            - '˪' # 0x02EA
            - 'ˢ' # 0x02E2
    selection_unicode_slashes: # forward slash alternatives
        CommandLine|contains:
            - '∕' # 0x22FF
            - '⁄' # 0x206F
    selection_unicode_hyphens: # hyphen alternatives
        CommandLine|contains:
            - '―' # 0x2015
            - '—' # 0x2014
    condition: 1 of selection*
falsepositives:
    - Unknown
level: high
