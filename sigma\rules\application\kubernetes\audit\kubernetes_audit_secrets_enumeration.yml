title: Kubernetes Secrets Enumeration
id: eeb3e9e1-b685-44e4-9232-6bb701f925b5
related:
    - id: 7ee0b4aa-d8d4-4088-b661-20efdf41a04c
      type: derived
status: test
description: Detects enumeration of Kubernetes secrets.
references:
    - https://microsoft.github.io/Threat-Matrix-for-Kubernetes/techniques/List%20K8S%20secrets/
author: <PERSON> (@laripping)
date: 2024-03-26
tags:
    - attack.t1552.007
    - attack.credential-access
logsource:
    category: application
    product: kubernetes
    service: audit
detection:
    selection:
        verb: 'list'
        objectRef.resource: 'secrets'
    condition: selection
falsepositives:
    - The Kubernetes dashboard occasionally accesses the kubernetes-dashboard-key-holder secret
level: low
