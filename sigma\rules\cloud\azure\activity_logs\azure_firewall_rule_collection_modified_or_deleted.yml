title: Azure Firewall Rule Collection Modified or Deleted
id: 025c9fe7-db72-49f9-af0d-31341dd7dd57
status: test
description: Identifies when Rule Collections (Application, NAT, and Network) is being modified or deleted.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON> @austinsonger
date: 2021-08-08
modified: 2022-08-23
tags:
    - attack.impact
    - attack.defense-evasion
    - attack.t1562.004
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.NETWORK/AZUREFIREWALLS/APPLICATIONRULECOLLECTIONS/WRITE
            - MICROSOFT.NETWORK/AZUREFIREWALLS/APPLICATIONRULECOLLECTIONS/DELETE
            - MICROSOFT.NETWORK/AZUREFIREWALLS/NATRULECOLLECTIONS/WRITE
            - MICROSOFT.NETWORK/AZUREFIREWALLS/NATRULECOLLECTIONS/DELETE
            - MICROSOFT.NETWORK/AZUREFIREWALLS/NETWORKRULECOLLECTIONS/WRITE
            - MICROSOFT.NETWORK/AZUREFIREWALLS/NETWORKRULECOLLECTIONS/DELETE
    condition: selection
falsepositives:
    - Rule Collections (Application, NAT, and Network) being modified or deleted may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Rule Collections (Application, NAT, and Network) modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
