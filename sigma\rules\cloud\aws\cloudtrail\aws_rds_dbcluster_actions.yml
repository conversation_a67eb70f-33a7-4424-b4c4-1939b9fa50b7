title: Modification or Deletion of an AWS RDS Cluster
id: 457cc9ac-d8e6-4d1d-8c0e-251d0f11a74c
status: experimental
description: Detects modifications to an RDS cluster or its deletion, which may indicate potential data exfiltration attempts, unauthorized access, or exposure of sensitive information.
references:
    - https://docs.aws.amazon.com/AmazonRDS/latest/APIReference/API_ModifyDBCluster.html
    - https://docs.aws.amazon.com/AmazonRDS/latest/APIReference/API_DeleteDBCluster.html
    - https://cloud.hacktricks.xyz/pentesting-cloud/aws-security/aws-privilege-escalation/aws-rds-privesc#rds-modifydbinstance
author: <PERSON>
date: 2024-12-06
tags:
    - attack.exfiltration
    - attack.t1020
logsource:
    product: aws
    service: cloudtrail
detection:
    selection:
        eventSource: rds.amazonaws.com
        eventName:
            - ModifyDBCluster
            - DeleteDBCluster
    condition: selection
falsepositives:
    - Verify if the modification or deletion was performed by an authorized administrator.
    - Confirm if the modification or deletion was part of a planned change or maintenance activity.
level: high
