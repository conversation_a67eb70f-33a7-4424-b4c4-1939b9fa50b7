title: Authentications To Important Apps Using Single Factor Authentication
id: f272fb46-25f2-422c-b667-45837994980f
status: test
description: Detect when authentications to important application(s) only required single-factor authentication
references:
    - https://learn.microsoft.com/en-gb/entra/architecture/security-operations-user-accounts
author: <PERSON><PERSON><PERSON><PERSON><PERSON>, '@dudders1'
date: 2022-07-28
tags:
    - attack.initial-access
    - attack.t1078
logsource:
    product: azure
    service: signinlogs
detection:
    selection:
        Status: 'Success'
        AppId: 'Insert Application ID use OR for multiple'
        AuthenticationRequirement: 'singleFactorAuthentication'
    condition: selection
falsepositives:
    - If this was approved by System Administrator.
level: medium
