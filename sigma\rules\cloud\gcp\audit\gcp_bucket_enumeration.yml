title: Google Cloud Storage Buckets Enumeration
id: e2feb918-4e77-4608-9697-990a1aaf74c3
status: test
description: Detects when storage bucket is enumerated in Google Cloud.
references:
    - https://cloud.google.com/storage/docs/json_api/v1/buckets
author: <PERSON> @austinsonger
date: 2021-08-14
modified: 2022-10-09
tags:
    - attack.discovery
logsource:
    product: gcp
    service: gcp.audit
detection:
    selection:
        gcp.audit.method_name:
            - storage.buckets.list
            - storage.buckets.listChannels
    condition: selection
falsepositives:
    - Storage Buckets being enumerated may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Storage Buckets enumerated from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: low
