RuleId;RuleName;MatchString
8e5e38e4-5350-4c0b-895a-e872ce0dd54f;Msiexec Initiated Connection;.*
ad1f4bb9-8dfb-4765-adb6-2a7cfb6c0f94;Suspicious WSMAN Provider Image Loads;.*
db809f10-56ce-4420-8c86-d6a7d793c79c;Raw Disk Access Using Illegitimate Tools;python-3
db809f10-56ce-4420-8c86-d6a7d793c79c;Raw Disk Access Using Illegitimate Tools;target\.exe
96f697b0-b499-4e5d-9908-a67bec11cdb6;Removal of Potential COM Hijacking Registry Keys;sharepointclient
96f697b0-b499-4e5d-9908-a67bec11cdb6;Removal of Potential COM Hijacking Registry Keys;odopen
1277f594-a7d1-4f28-a2d3-73af5c<PERSON>ab43;Windows Shell File Write to Suspicious Folder;Computer: Agamemnon
e28a5a99-da44-436d-b7a0-2afc20a5f413;Whoami Execution;WindowsPowerShell
8ac03a65-6c84-4116-acad-dc1558ff7a77;Sysmon Configuration Change;sysmon-intense\.xml
8ac03a65-6c84-4116-acad-dc1558ff7a77;Sysmon Configuration Change;Computer: (evtx-PC|Agamemnon)
4358e5a5-7542-4dcb-b9f3-87667371839b;ISO or Image Mount Indicator in Recent Files;_Office_Professional_Plus_
36480ae1-a1cb-4eaa-a0d6-29801d7e9142;Renamed Binary;WinRAR
73bba97f-a82d-42ce-b315-9182e76c57b1;Imports Registry Key From a File;Evernote
6741916F-B4FA-45A0-8BF8-8249C702033A;Added Rule in Windows Firewall with Advanced Security;\\Integration\\Integrator\.exe
00bb5bd5-1379-4fcf-a965-a5b6f7478064;Setting Change in Windows Firewall with Advanced Security;Level: 4  Task: 0
162ab1e4-6874-4564-853c-53ec3ab8be01;TeamViewer Remote Session;TeamViewer(_Service)?\.exe
cdc8da7d-c303-42f8-b08c-b4ab47230263;Rundll32 Internet Connection;20\.49\.150\.241
bef0bc5a-b9ae-425d-85c6-7b2d705980c6;Python Initiated Connection;151\.101\.64\.223
9711de76-5d4f-4c50-a94f-21e4e8f8384d;Installation of TeamViewer Desktop;TeamViewer_Desktop\.exe
96f697b0-b499-4e5d-9908-a67bec11cdb6;Removal of Potential COM Hijacking Registry Keys;target\.exe
9494479d-d994-40bf-a8b1-eea890237021;Scheduled Task Creation From Potential Suspicious Parent Location;.*
81325ce1-be01-4250-944f-b4789644556f;Suspicius Schtasks From Env Var Folder;TVInstallRestore
6ea3bf32-9680-422d-9f50-e90716b12a66;UAC Bypass Via Wsreset;EventType: DeleteKey
43f487f0-755f-4c2a-bce7-d6d2eec2fcf8;Suspicious Add Scheduled Task From User AppData Temp;TVInstallRestore
c187c075-bb3e-4c62-b4fa-beae0ffc211f;Deteled Rule in Windows Firewall with Advanced Security;Dropbox.*\\netsh\.exe
69aeb277-f15f-4d2d-b32a-55e883609563;Disabling Windows Event Auditing;Computer: .*
ac175779-025a-4f12-98b0-acdaeb77ea85;PowerShell Script Run in AppData;\\Evernote-
1f2b5353-573f-4880-8e33-7d04dcf97744;Sysmon Configuration Modification;Computer: evtx-PC
734f8d9b-42b8-41b2-bcf5-abaf49d5a3c8;Remote PowerShell Session Host Process (WinRM);WIN-FPV0DSIC9O6
734f8d9b-42b8-41b2-bcf5-abaf49d5a3c8;Remote PowerShell Session Host Process (WinRM);Computer: Agamemnon
a96970af-f126-420d-90e1-d37bf25e50e1;Use Short Name Path in Image;Ninite\.exe
349d891d-fef0-4fe4-bc53-eee623a15969;Use Short Name Path in Command Line;Ninite\.exe
a96970af-f126-420d-90e1-d37bf25e50e1;Use Short Name Path in Image;target\.exe
349d891d-fef0-4fe4-bc53-eee623a15969;Use Short Name Path in Command Line;target\.exe
a96970af-f126-420d-90e1-d37bf25e50e1;Use Short Name Path in Image;unzip\.exe
349d891d-fef0-4fe4-bc53-eee623a15969;Use Short Name Path in Command Line;TeamViewer_\.exe
7a02e22e-b885-4404-b38b-1ddc7e65258a;Suspicious Schtasks Schedule Type;TeamViewer_\.exe
949f1ffb-6e85-4f00-ae1e-c3c5b190d605;Explorer Process Tree Break;Computer: Agamemnon
fdbf0b9d-0182-4c43-893b-a1eaab92d085;Newly Registered Protocol Handler;.*
100ef69e-3327-481c-8e5c-6d80d9507556;System Eventlog Cleared;.*
52a85084-6989-40c3-8f32-091e12e17692;Suspicious Usage of CVE_2021_34484 or CVE 2022_21919;Computer: Agamemnon
573df571-a223-43bc-846e-3f98da481eca;Copy a File Downloaded From Internet;7z\.exe
37774c23-25a1-4adb-bb6d-8bb9fd59c0f8;Image Load of VSS Dll by Uncommon Executable;SetupFrontEnd\.exe
1a31b18a-f00c-4061-9900-f735b96c99fc;Remote Access Tool Services Have Been Installed - System;ServiceName: TeamViewer
c8b00925-926c-47e3-beea-298fd563728e;Remote Access Tool Services Have Been Installed - Security;ServiceName: TeamViewer
b69888d4-380c-45ce-9cf9-d9ce46e67821;Executable in ADS;msedge\.exe 
b69888d4-380c-45ce-9cf9-d9ce46e67821;Executable in ADS;firefox\.exe 
b69888d4-380c-45ce-9cf9-d9ce46e67821;Executable in ADS;7z\.exe
65236ec7-ace0-4f0c-82fd-737b04fd4dcb;EVTX Created In Uncommon Location;powershell\.exe
a62b37e0-45d3-48d9-a517-90c1a1b0186b;Eventlog Cleared;Computer: DESKTOP-A8CALR3
a62b37e0-45d3-48d9-a517-90c1a1b0186b;Eventlog Cleared;Computer: WIN-06FB45IHQ35
4eec988f-7bf0-49f1-8675-1e6a510b3a2a;Potential PendingFileRenameOperations Tamper;target\.exe
4eec988f-7bf0-49f1-8675-1e6a510b3a2a;Potential PendingFileRenameOperations Tamper;target\.tmp
48bfd177-7cf2-412b-ad77-baf923489e82;Image Load of VSS Dll by Uncommon Executable;SetupFrontEnd.exe
87911521-7098-470b-a459-9a57fc80bdfd;Sysmon Configuration Updated;.*
0eb46774-f1ab-4a74-8238-1155855f2263;Disable Windows Defender Functionalities Via Registry Keys;.*
e9d4ab66-a532-4ef7-a502-66a9e4a34f5d;NTLMv1 Logon Between Client and Server;.*
ccb5742c-c248-4982-8c5c-5571b9275ad3;Potential Suspicious Findstr.EXE Execution;httpd\.exe
9ae01559-cf7e-4f8e-8e14-4c290a1b4784;CredUI.DLL Load By Uncommon Process;Spotify\.exe
52182dfb-afb7-41db-b4bc-5336cb29b464;Suspicious File Download From File Sharing Websites;objects\.githubusercontent\.com
