title: Mimi<PERSON>z Detection LSASS Access
id: 0d894093-71bc-43c3-8c4d-ecfc28dcf5d9
status: deprecated
description: Detects process access to LSASS which is typical for Mimikatz (0x1000 PROCESS_QUERY_ LIMITED_INFORMATION, 0x0400 PROCESS_QUERY_ INFORMATION "only old
    versions", 0x0010 PROCESS_VM_READ)
references:
    - https://onedrive.live.com/view.aspx?resid=D026B4699190F1E6!2843&ithint=file%2cpptx&app=PowerPoint&authkey=!AMvCRTKB_V1J5ow
    - https://web.archive.org/web/20230208123920/https://cyberwardog.blogspot.com/2017/03/chronicles-of-threat-hunter-hunting-for_22.html
tags:
    - attack.t1003
    - attack.s0002
    - attack.credential_access
    - car.2019-04-004
author: <PERSON><PERSON><PERSON>
date: 2017/10/18
modified: 2022/04/11
logsource:
    product: windows
    category: process_access
detection:
    selection:
        TargetImage|endswith: '\lsass.exe'
        GrantedAccess:
            - '0x1410'
            - '0x1010'
            - '0x410'
    filter:
        SourceImage|startswith: 
            - 'C:\Program Files\WindowsApps\'
            - 'C:\Windows\System32\'
        SourceImage|endswith: '\GamingServices.exe'
    condition: selection and not filter
fields:
    - ComputerName
    - User
    - SourceImage
falsepositives:
    - Some security products access LSASS in this way.
level: high
