title: OneLogin User Account Locked
id: a717c561-d117-437e-b2d9-0118a7035d01
status: test
description: Detects when an user account is locked or suspended.
references:
    - https://developers.onelogin.com/api-docs/1/events/event-resource/
author: <PERSON> @austinsonger
date: 2021-10-12
modified: 2022-12-25
tags:
    - attack.impact
logsource:
    product: onelogin
    service: onelogin.events
detection:
    selection1: # Locked via API
        event_type_id: 532
    selection2: # Locked via API
        event_type_id: 553
    selection3: # Suspended via API
        event_type_id: 551
    condition: 1 of selection*
falsepositives:
    - System may lock or suspend user accounts.
level: low
