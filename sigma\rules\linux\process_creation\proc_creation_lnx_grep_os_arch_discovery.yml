title: OS Architecture Discovery Via Grep
id: d27ab432-2199-483f-a297-03633c05bae6
status: test
description: |
    Detects the use of grep to identify information about the operating system architecture. Often combined beforehand with the execution of "uname" or "cat /proc/cpuinfo"
references:
    - https://blogs.jpcert.or.jp/en/2023/05/gobrat.html
    - https://jstnk9.github.io/jstnk9/research/GobRAT-Malware/
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
author: <PERSON><PERSON><PERSON>, @Joseliyo_Jstnk
date: 2023-06-02
tags:
    - attack.discovery
    - attack.t1082
logsource:
    category: process_creation
    product: linux
detection:
    selection_process:
        Image|endswith: '/grep'
    selection_architecture:
        CommandLine|endswith:
            - 'aarch64'
            - 'arm'
            - 'i386'
            - 'i686'
            - 'mips'
            - 'x86_64'
    condition: all of selection_*
falsepositives:
    - Unknown
level: low
