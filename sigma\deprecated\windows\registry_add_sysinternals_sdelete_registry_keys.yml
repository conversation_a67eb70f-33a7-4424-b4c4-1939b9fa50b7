title: Sysinternals SDelete Registry Keys
id: 9841b233-8df8-4ad7-9133-b0b4402a9014
status: deprecated
description: A General detection to trigger for the creation or modification of .*\Software\Sysinternals\SDelete registry keys. Indicators of the use of Sysinternals SDelete tool.
references:
    - https://github.com/OTRF/detection-hackathon-apt29/issues/9
    - https://github.com/OTRF/ThreatHunter-Playbook/blob/****************************************/docs/evals/apt29/detections/4.B.2_59A9AC92-124D-4C4B-A6BF-3121C98677C3.md
author: <PERSON> (Cyb3rWard0g), OTR (Open Threat Research)
date: 2020/05/02
modified: 2023/02/07
tags:
    - attack.defense_evasion
    - attack.t1070.004
logsource:
    product: windows
    category: registry_add
detection:
    selection:
        EventType: CreateKey
        TargetObject|contains: '\Software\Sysinternals\SDelete'
    condition: selection
falsepositives:
    - Unknown
level: medium
