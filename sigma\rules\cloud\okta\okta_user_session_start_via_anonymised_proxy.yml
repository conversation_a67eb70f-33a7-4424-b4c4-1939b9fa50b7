title: Okta User Session Start Via An Anonymising Proxy Service
id: bde30855-5c53-4c18-ae90-1ff79ebc9578
status: test
description: Detects when an Okta user session starts where the user is behind an anonymising proxy service.
references:
    - https://developer.okta.com/docs/reference/api/system-log/
    - https://sec.okta.com/articles/2023/08/cross-tenant-impersonation-prevention-and-detection
author: kelnage
date: 2023-09-07
tags:
    - attack.defense-evasion
    - attack.t1562.006
logsource:
    product: okta
    service: okta
detection:
    selection:
        eventtype: 'user.session.start'
        securitycontext.isproxy: 'true'
    condition: selection
falsepositives:
    - If a user requires an anonymising proxy due to valid justifications.
level: high
