title: Invoke-Obfuscation Via Use Rundll32
id: 36c5146c-d127-4f85-8e21-01bf62355d5a
status: deprecated
description: Detects Obfuscated Powershell via use Rundll32 in Scripts
references:
    - https://github.com/SigmaHQ/sigma/issues/1009
author: <PERSON><PERSON>, oscd.community
date: 2019/10/08
modified: 2022/12/30
tags:
    - attack.defense_evasion
    - attack.t1027
    - attack.execution
    - attack.t1059.001
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        CommandLine|contains|all:
            - '&&'
            - 'rundll32'
            - 'shell32.dll'
            - 'shellexec_rundll'
        CommandLine|contains:
            - 'value'
            - 'invoke'
            - 'comspec'
            - 'iex'
    condition: selection
falsepositives:
    - Unknown
level: high
