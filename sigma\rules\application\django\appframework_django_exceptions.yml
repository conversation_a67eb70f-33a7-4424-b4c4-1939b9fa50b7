title: Django Framework Exceptions
id: fd435618-981e-4a7c-81f8-f78ce480d616
status: stable
description: Detects suspicious Django web application framework exceptions that could indicate exploitation attempts
references:
    - https://docs.djangoproject.com/en/1.11/ref/exceptions/
    - https://docs.djangoproject.com/en/1.11/topics/logging/#django-security
author: <PERSON>
date: 2017-08-05
modified: 2020-09-01
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: django
detection:
    keywords:
        - SuspiciousOperation
        # Subclasses of SuspiciousOperation
        - DisallowedHost
        - DisallowedModelAdminLookup
        - DisallowedModelAdminToField
        - DisallowedRedirect
        - InvalidSessionKey
        - RequestDataTooBig
        - SuspiciousFileOperation
        - SuspiciousMultipartForm
        - SuspiciousSession
        - TooManyFieldsSent
        # Further security-related exceptions
        - PermissionDenied
    condition: keywords
falsepositives:
    - Application bugs
level: medium
