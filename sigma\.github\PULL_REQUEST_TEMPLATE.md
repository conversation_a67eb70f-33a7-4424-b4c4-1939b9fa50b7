<!--
Thanks for your contribution. Please make sure to fill the contents of this template with the necessary information to ease and speed up the review process.

!!! PLEASE DO NOT DELETE ANY SECTION, COMMENT OR THE CONTENT OF THE TEMPLATE. !!!
-->

### Summary of the Pull Request

<!--
**Please note that this section is required and must be filled**
A short summary of your pull request.
-->

### Changelog

<!--
** Don't remove this comment **
You need to add one line for every changed file of the PR and prefix one of the following tags:
new:	<title>
update:	<title> - <optional comment>
fix:	<title> - <optional comment>
remove:	<title> - <optional comment>
chore: for non-detection related changes (e.g. dates/titles) and changes on workflow

e.g.
new: Brute-Force Attacks on Azure Admin Account
update: Suspicious Microsoft Office Child Process - add MSPUB.EXE
fix: Malware User Agent - remove legitimate Firefox UA
chore: workflow - update checkout version
remove: Suspicious Office Execution - deprecated in favour of 8f922766-a1d3-4b57-9966-b27de37fddd2
-->

### Example Log Event

<!--
Fill this in case of false positive fixes
-->

### Fixed Issues

<!--
Link the fixed issues here, in case your commit fixes issues with rules or code
-->

### SigmaHQ Rule Creation Conventions

- If your PR adds new rules, please consider following and applying these [conventions](https://github.com/SigmaHQ/sigma-specification/blob/main/sigmahq/)
