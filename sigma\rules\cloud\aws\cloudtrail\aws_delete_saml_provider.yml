title: AWS SAML Provider Deletion Activity
id: ccd6a6c8-bb4e-4a91-9d2a-07e632819374
status: experimental
description: |
    Detects the deletion of an AWS SAML provider, potentially indicating malicious intent to disrupt administrative or security team access.
    An attacker can remove the SAML provider for the information security team or a team of system administrators, to make it difficult for them to work and investigate at the time of the attack and after it.
references:
    - https://docs.aws.amazon.com/IAM/latest/APIReference/API_DeleteSAMLProvider.html
author: <PERSON>
date: 2024-12-19
tags:
    - attack.t1078.004
    - attack.privilege-escalation
    - attack.t1531
    - attack.impact
logsource:
    product: aws
    service: cloudtrail
detection:
    selection:
        eventSource: 'iam.amazonaws.com'
        eventName: 'DeleteSAMLProvider'
        status: 'success'
    condition: selection
falsepositives:
    - Automated processes using tools like Terraform may trigger this alert.
    - Legitimate administrative actions by authorized system administrators could cause this alert. Verify the user identity, user agent, and hostname to ensure they are expected.
    - Deletions by unfamiliar users should be investigated. If the behavior is known and expected, it can be exempted from the rule.
level: medium
