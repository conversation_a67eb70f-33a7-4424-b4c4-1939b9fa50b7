title: Change to Authentication Method
id: 4d78a000-ab52-4564-88a5-7ab5242b20c7
status: test
description: Change to authentication method could be an indicator of an attacker adding an auth method to the account so they can have continued access.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-privileged-accounts
author: AlertIQ
date: 2021-10-10
modified: 2022-12-25
tags:
    - attack.credential-access
    - attack.t1556
    - attack.persistence
    - attack.defense-evasion
    - attack.t1098
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        LoggedByService: 'Authentication Methods'
        Category: 'UserManagement'
        OperationName: 'User registered security info'
    condition: selection
falsepositives:
    - Unknown
level: medium
