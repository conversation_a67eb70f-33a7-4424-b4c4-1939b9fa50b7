title: Azure Application Deleted
id: 410d2a41-1e6d-452f-85e5-abdd8257a823
status: test
description: Identifies when a application is deleted in Azure.
references:
    - https://learn.microsoft.com/en-us/entra/identity/monitoring-health/reference-audit-activities#application-proxy
author: <PERSON> @austinsonger
date: 2021-09-03
modified: 2022-10-09
tags:
    - attack.defense-evasion
    - attack.impact
    - attack.t1489
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        properties.message:
            - Delete application
            - Hard Delete application
    condition: selection
falsepositives:
    - Application being deleted may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Application deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
