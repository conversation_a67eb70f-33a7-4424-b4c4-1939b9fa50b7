title: Ruby on Rails Framework Exceptions
id: 0d2c3d4c-4b48-4ac3-8f23-ea845746bb1a
status: stable
description: Detects suspicious Ruby on Rails exceptions that could indicate exploitation attempts
references:
    - http://edgeguides.rubyonrails.org/security.html
    - http://guides.rubyonrails.org/action_controller_overview.html
    - https://stackoverflow.com/questions/25892194/does-rails-come-with-a-not-authorized-exception
    - https://github.com/rails/rails/blob/cd08e6bcc4cd8948fe01e0be1ea0c7ca60373a25/actionpack/lib/action_dispatch/middleware/exception_wrapper.rb
author: <PERSON>
date: 2017-08-06
modified: 2020-09-01
tags:
    - attack.initial-access
    - attack.t1190
logsource:
    category: application
    product: ruby_on_rails
detection:
    keywords:
        - ActionController::InvalidAuthenticityToken
        - ActionController::InvalidCrossOriginRequest
        - ActionController::MethodNotAllowed
        - ActionController::BadRequest
        - ActionController::ParameterMissing
    condition: keywords
falsepositives:
    - Application bugs
level: medium
