title: Sign-in Failure Due to Conditional Access Requirements Not Met
id: b4a6d707-9430-4f5f-af68-0337f52d5c42
status: test
description: Define a baseline threshold for failed sign-ins due to Conditional Access failures
references:
    - https://learn.microsoft.com/en-gb/entra/architecture/security-operations-privileged-accounts
author: <PERSON><PERSON><PERSON>, '@Yochana-H'
date: 2022-06-01
tags:
    - attack.initial-access
    - attack.credential-access
    - attack.t1110
    - attack.t1078.004
logsource:
    product: azure
    service: signinlogs
detection:
    selection:
        ResultType: 53003
        Resultdescription: Blocked by Conditional Access
    condition: selection
falsepositives:
    - Service Account misconfigured
    - Misconfigured Systems
    - Vulnerability Scanners
level: high
