title: Azure Application Credential Modified
id: cdeef967-f9a1-4375-90ee-6978c5f23974
status: test
description: Identifies when a application credential is modified.
references:
    - https://www.cloud-architekt.net/auditing-of-msi-and-service-principals/
author: <PERSON> @austinsonger
date: 2021-09-02
modified: 2022-10-09
tags:
    - attack.impact
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        properties.message: 'Update application - Certificates and secrets management'
    condition: selection
falsepositives:
    - Application credential added may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Application credential added from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
