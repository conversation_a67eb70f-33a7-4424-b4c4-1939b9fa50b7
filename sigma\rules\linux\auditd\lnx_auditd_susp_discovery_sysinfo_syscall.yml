title: System Info Discovery via Sysinfo Syscall
id: b207d563-a1d9-4275-b349-77d1eb55aa6d
status: experimental
description: |
    Detects use of the sysinfo system call in Linux, which provides a snapshot of key system statistics such as uptime, load averages, memory usage, and the number of running processes.
    Malware or reconnaissance tools might leverage sysinfo to fingerprint the system - gathering data to determine if it's a viable target.
references:
    - https://github.com/CheraghiMilad/bypass-Neo23x0-auditd-config/blob/f1c478a37911a5447d5ffcd580f22b167bf3df14/sysinfo-syscall/README.md
    - https://man7.org/linux/man-pages/man2/sysinfo.2.html
author: <PERSON><PERSON>
date: 2025-05-30
tags:
    - attack.discovery
    - attack.t1057
    - attack.t1082
logsource:
    product: linux
    service: auditd
    definition: |
        Required auditd configuration:
        -a always,exit -F arch=b64 -S sysinfo -k discovery_sysinfo_syscall
        -a always,exit -F arch=b32 -S sysinfo -k discovery_sysinfo_syscall
detection:
    selection:
        type: 'SYSCALL'
        syscall: 'sysinfo'
    filter_optional_splunk:
        exe|endswith: '/bin/splunkd'
    condition: selection and not 1 of filter_optional_*
falsepositives:
    - Legitimate administrative activity
level: low
