title: Suspicious Execution of Sc to Delete AV Services
id: 7fd4bb39-12d0-45ab-bb36-cebabc73dc7b
status: deprecated
description: Detects when attackers use "sc.exe" to delete AV services from the system in order to avoid detection
references:
    - https://www.virustotal.com/gui/file/****************************************************************
author: <PERSON><PERSON><PERSON><PERSON> (Nextron Systems)
date: 2022/08/01
modified: 2023/03/04
tags:
    - attack.execution
    - attack.defense_evasion
    - attack.t1562.001
logsource:
    category: process_creation
    product: windows
detection:
    selection_img:
        - Image|endswith: '\sc.exe'
        - OriginalFileName: 'sc.exe'
    selection_cli:
        CommandLine|contains: ' delete '
    selection_av_process:
        CommandLine|contains:
            # Delete Service 'AVG'
            - 'AvgAdminServer'
            - 'AVG Antivirus'
            - 'MBEndpointAgent'
            # Delete Service 'Malwarebytes'
            - 'MBAMService'
            - 'MBCloudEA'
            - 'avgAdminClient'
            # Delete Service 'Sophos'
            - 'SAVService'
            - 'SAVAdminService'
            - 'Sophos AutoUpdate Service'
            - 'Sophos Clean Service'
            - 'Sophos Device Control Service'
            - 'Sophos File Scanner Service'
            - 'Sophos Health Service'
            - 'Sophos MCS Agent'
            - 'Sophos MCS Client'
            - 'SntpService'
            - 'swc_service'
            - 'swi_service'
            - 'Sophos UI'
            - 'swi_update'
            - 'Sophos Web Control Service'
            - 'Sophos System Protection Service'
            - 'Sophos Safestore Service'
            - 'hmpalertsvc'
            - 'RpcEptMapper'
            - 'Sophos Endpoint Defense Service'
            - 'SophosFIM'
            - 'swi_filter'
            # Delete Service 'FireBird'
            - 'FirebirdGuardianDefaultInstance'
            - 'FirebirdServerDefaultInstance'
            # Delete Service 'Webroot'
            - 'WRSVC'
            # Delete Service 'ESET'
            - 'ekrn'
            - 'ekrnEpsw'
            # Delete Service 'Kaspersky'
            - 'klim6'
            - 'AVP18.0.0'
            - 'KLIF'
            - 'klpd'
            - 'klflt'
            - 'klbackupdisk'
            - 'klbackupflt'
            - 'klkbdflt'
            - 'klmouflt'
            - 'klhk'
            - 'KSDE1.0.0'
            - 'kltap'
            # Delete Service 'Quick Heal'
            - 'ScSecSvc'
            - 'Core Mail Protection'
            - 'Core Scanning Server'
            - 'Core Scanning ServerEx'
            - 'Online Protection System'
            - 'RepairService'
            - 'Core Browsing Protection'
            - 'Quick Update Service'
            # Delete Service 'McAfee'
            - 'McAfeeFramework'
            - 'macmnsvc'
            - 'masvc'
            - 'mfemms'
            - 'mfevtp'
            # Delete Service 'Trend Micro'
            - 'TmFilter'
            - 'TMLWCSService'
            - 'tmusa'
            - 'TmPreFilter'
            - 'TMSmartRelayService'
            - 'TMiCRCScanService'
            - 'VSApiNt'
            - 'TmCCSF'
            - 'tmlisten'
            - 'TmProxy'
            - 'ntrtscan'
            - 'ofcservice'
            - 'TmPfw'
            - 'PccNTUpd'
            # Delete Service 'Panda'
            - 'PandaAetherAgent'
            - 'PSUAService'
            - 'NanoServiceMain'
            - 'EPIntegrationService'
            - 'EPProtectedService'
            - 'EPRedline'
            - 'EPSecurityService'
            - 'EPUpdateService'
    condition: all of selection*
falsepositives:
    - Legitimate software deleting using the same method of deletion (Add it to a filter if you find cases as such)
level: high
