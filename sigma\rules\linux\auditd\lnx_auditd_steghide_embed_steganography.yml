title: Steganography Hide Files with Steghide
id: ce446a9e-30b9-4483-8e38-d2c9ad0a2280
status: test
description: Detects embedding of files with usage of steghide binary, the adversaries may use this technique to prevent the detection of hidden information.
references:
    - https://vitux.com/how-to-hide-confidential-files-in-images-on-debian-using-steganography/
author: '<PERSON><PERSON><PERSON>'
date: 2021-09-11
modified: 2022-10-09
tags:
    - attack.defense-evasion
    - attack.t1027.003
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: EXECVE
        a0: steghide
        a1: embed
        a2:
            - '-cf'
            - '-ef'
        a4:
            - '-cf'
            - '-ef'
    condition: selection
falsepositives:
    - Unknown
level: low
