title: User Added To Admin Group - MacOS
id: 0c1ffcf9-efa9-436e-ab68-23a9496ebf5b
status: deprecated
description: Detects attempts to create and/or add an account to the admin group, thus granting admin privileges.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/master/atomics/T1078.003/T1078.003.md#atomic-test-3---create-local-account-with-admin-privileges-using-sysadminctl-utility---macos
    - https://ss64.com/osx/dscl.html
    - https://ss64.com/osx/sysadminctl.html
author: <PERSON><PERSON> (D4rkCiph3r)
date: 2023/03/19
modified: 2023/08/22
tags:
    - attack.t1078.003
    - attack.initial_access
    - attack.privilege_escalation
logsource:
    category: process_creation
    product: macos
detection:
    selection_sysadminctl: #creates and adds new user to admin group
        Image|endswith: '/sysadminctl'
        CommandLine|contains|all:
            - ' -addUser '
            - ' -admin '
    selection_dscl: #adds to admin group
        Image|endswith: '/dscl'
        CommandLine|contains|all:
            - ' -append '
            - ' /Groups/admin '
            - ' GroupMembership '
    condition: 1 of selection_*
falsepositives:
    - Legitimate administration activities
level: medium
