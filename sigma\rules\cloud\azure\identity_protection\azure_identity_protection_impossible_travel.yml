title: Impossible Travel
id: b2572bf9-e20a-4594-b528-40bde666525a
status: test
description: Identifies user activities originating from geographically distant locations within a time period shorter than the time it takes to travel from the first location to the second.
references:
    - https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#impossible-travel
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-user-accounts#unusual-sign-ins
author: <PERSON> '@markmorow', <PERSON>, '@gleeiamglo'
date: 2023-09-03
tags:
    - attack.t1078
    - attack.persistence
    - attack.defense-evasion
    - attack.privilege-escalation
    - attack.initial-access
logsource:
    product: azure
    service: riskdetection
detection:
    selection:
        riskEventType: 'impossibleTravel'
    condition: selection
falsepositives:
    - Connecting to a VPN, performing activity and then dropping and performing additional activity.
level: high
