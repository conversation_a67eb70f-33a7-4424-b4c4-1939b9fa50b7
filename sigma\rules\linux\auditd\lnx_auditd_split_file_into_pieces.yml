title: Split A File Into Pieces - Linux
id: 2dad0cba-c62a-4a4f-949f-5f6ecd619769
status: test
description: 'Detection use of the command "split" to split files into parts and possible transfer.'
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1030/T1030.md
author: '<PERSON>, oscd.community'
date: 2020-10-15
modified: 2022-11-28
tags:
    - attack.exfiltration
    - attack.t1030
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'SYSCALL'
        comm: 'split'
    condition: selection
falsepositives:
    - Legitimate administrative activity
level: low
