title: Communication To LocaltoNet Tunneling Service Initiated - Linux
id: c4568f5d-131f-4e78-83d4-45b2da0ec4f1
status: test
description: |
    Detects an executable initiating a network connection to "LocaltoNet" tunneling sub-domains.
    LocaltoNet is a reverse proxy that enables localhost services to be exposed to the Internet.
    Attackers have been seen to use this service for command-and-control activities to bypass MFA and perimeter controls.
references:
    - https://localtonet.com/documents/supported-tunnels
    - https://cloud.google.com/blog/topics/threat-intelligence/unc3944-targets-saas-applications
author: <PERSON> (mnemonic.io)
date: 2024-06-17
tags:
    - attack.command-and-control
    - attack.t1572
    - attack.t1090
    - attack.t1102
logsource:
    category: network_connection
    product: linux
detection:
    selection:
        DestinationHostname|endswith:
            - '.localto.net'
            - '.localtonet.com'
        Initiated: 'true'
    condition: selection
falsepositives:
    - Legitimate use of the LocaltoNet service.
level: high
