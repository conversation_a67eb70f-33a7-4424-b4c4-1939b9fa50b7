title: Remote Event Log Recon
id: 2053961f-44c7-4a64-b62d-f6e72800af0d
status: test
description: Detects remote RPC calls to get event log information via EVEN or EVEN6
references:
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, <PERSON><PERSON>
date: 2022-01-01
tags:
    - attack.discovery
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:82273fdc-e32a-18c3-3f78-827929dc23ea and uuid:f6beaff7-1e19-4fbb-9f8f-b89e2018337c"'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid:
            - 82273fdc-e32a-18c3-3f78-827929dc23ea
            - f6beaff7-1e19-4fbb-9f8f-b89e2018337c
    condition: selection
falsepositives:
    - Remote administrative tasks on Windows Events
level: high
