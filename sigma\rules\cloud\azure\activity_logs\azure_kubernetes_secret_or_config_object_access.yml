title: Azure Kubernetes Secret or Config Object Access
id: 7ee0b4aa-d8d4-4088-b661-20efdf41a04c
status: test
description: Identifies when a Kubernetes account access a sensitive objects such as configmaps or secrets.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations#microsoftkubernetes
    - https://www.microsoft.com/security/blog/2021/03/23/secure-containerized-environments-with-updated-threat-matrix-for-kubernetes/
    - https://www.microsoft.com/security/blog/2020/04/02/attack-matrix-kubernetes/
    - https://medium.com/mitre-engenuity/att-ck-for-containers-now-available-4c2359654bf1
author: <PERSON> @austinsonger
date: 2021-08-07
modified: 2022-08-23
tags:
    - attack.impact
    - attack.t1485
    - attack.t1496
    - attack.t1489
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.KUBERNETES/CONNECTEDCLUSTERS/CONFIGMAPS/WRITE
            - MICROSOFT.KUBERNETES/CONNECTEDCLUSTERS/CONFIGMAPS/DELETE
            - MICROSOFT.KUBERNETES/CONNECTEDCLUSTERS/SECRETS/WRITE
            - MICROSOFT.KUBERNETES/CONNECTEDCLUSTERS/SECRETS/DELETE
    condition: selection
falsepositives:
    - Sensitive objects may be accessed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment. Sensitive objects accessed from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
