name: Generate Updated ATT&CK Heatmap
on:
  schedule:
    - cron: "0 0 1 * *"
  workflow_dispatch:


jobs:
  generate-heatmap:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Install Sigma
        run: pipx install sigma-cli

      - name: Update Heatmap
        run: sigma analyze attack count ./other/sigma_attack_nav_coverage.json rule*

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          reviewers: nasbench, frack113, phantinuss
          delete-branch: true
          commit-message: 'chore: update ATT&CK heatmap'
          branch: 'create-pull-request/update-heatmap'
          title: 'Update ATT&CK Heatmap Coverage'
          body: |
            ### Summary of the Pull Request

            This PR updates sigma_attack_nav_coverage.json to reflect the current rule coverage.
            To generate a new SVG file, go to the [MITRE ATT&CK Navigator](https://mitre-attack.github.io/attack-navigator/#layerURL=https://raw.githubusercontent.com/SigmaHQ/sigma/master/other/sigma_attack_nav_coverage.json) and export a SVG via "Layer Controls" > "Export" (download icon) > "render layer to SVG".

            ### Changelog

            chore: update ATT&CK heatmap

            ### Example Log Event

            N/A

            ### Fixed Issues

            N/A

            ### SigmaHQ Rule Creation Conventions

            - If your PR adds new rules, please consider following and applying these [conventions](https://github.com/SigmaHQ/sigma-specification/blob/main/sigmahq/sigmahq_conventions.md)
