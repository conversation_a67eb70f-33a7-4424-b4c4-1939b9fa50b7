title: Potential Persistence Via COM Search Order Hijacking
id: a0ff33d8-79e4-4cef-b4f3-9dc4133ccd12
related:
    - id: 790317c0-0a36-4a6a-a105-6e576bf99a14
      type: derived
status: deprecated
description: Detects potential COM object hijacking leveraging the COM Search Order
references:
    - https://www.cyberbit.com/blog/endpoint-security/com-hijacking-windows-overlooked-security-vulnerability/
author: <PERSON><PERSON> (@0xThiebaut), oscd.community, Cédric Hien
date: 2020-04-14
modified: 2024-09-02
tags:
    - attack.persistence
    - attack.t1546.015
logsource:
    category: registry_set
    product: windows
detection:
    selection: # Detect new COM servers in the user hive
        TargetObject|contains: '\CLSID\'
        TargetObject|endswith: '\InprocServer32\(Default)'
    filter_main_generic:
        Details|contains: # Exclude privileged directories and observed FPs
            - '%%systemroot%%\system32\'
            - '%%systemroot%%\SysWow64\'
    filter_main_onedrive:
        Details|contains:
            # Related To OneDrive
            - '\AppData\Local\Microsoft\OneDrive\'
            - '\FileCoAuthLib64.dll'
            - '\FileSyncShell64.dll'
            - '\FileSyncApi64.dll'
    filter_main_health_service:
        Image|endswith: ':\WINDOWS\system32\SecurityHealthService.exe'
    filter_main_teams:
        Details|contains|all:
            - '\AppData\Local\Microsoft\TeamsMeetingAddin\'
            - '\Microsoft.Teams.AddinLoader.dll'
    filter_main_dropbox:
        Details|contains|all:
            - '\AppData\Roaming\Dropbox\'
            - '\DropboxExt64.*.dll'
    filter_main_trend_micro:
        Details|endswith: 'TmopIEPlg.dll' # TrendMicro osce
    filter_main_update:
        Image|endswith:
            - ':\WINDOWS\system32\wuauclt.exe'
            - ':\WINDOWS\system32\svchost.exe'
    filter_main_defender:
        Image|contains:
            - ':\ProgramData\Microsoft\Windows Defender\Platform\'
            - ':\Program Files\Windows Defender\'
        Image|endswith: '\MsMpEng.exe'
    filter_main_nvidia:
        Details|contains: '\FileRepository\nvmdi.inf'
    filter_main_edge:
        Image|endswith: '\MicrosoftEdgeUpdateComRegisterShell64.exe'
    filter_main_dx:
        Image|endswith: ':\WINDOWS\SYSTEM32\dxdiag.exe'
    filter_main_python:
        Details|endswith:
            - ':\Windows\pyshellext.amd64.dll'
            - ':\Windows\pyshellext.dll'
    filter_main_bonjourlib:
        Details|endswith:
            - ':\Windows\system32\dnssdX.dll'
            - ':\Windows\SysWOW64\dnssdX.dll'
    filter_main_printextensionmanager:
        Details|endswith: ':\Windows\system32\spool\drivers\x64\3\PrintConfig.dll'
    filter_main_programfiles:
        Details|contains:
            - ':\Program Files\'
            - ':\Program Files (x86)\'
    filter_main_programdata:
        Details|contains: ':\ProgramData\Microsoft\'
    filter_main_gameservice:
        Details|contains: ':\WINDOWS\system32\GamingServicesProxy.dll'
    filter_main_poqexec:
        Image|endswith: ':\Windows\System32\poqexec.exe'
        Details|contains: ':\Windows\System32\Autopilot.dll'
    filter_main_sec_health_svc:
        Image|endswith: ':\Windows\system32\SecurityHealthService.exe'
        Details|contains: ':\Windows\System32\SecurityHealth'
    filter_main_inprocserver:
        Image|endswith:
            - ':\Windows\System32\poqexec.exe'
            - ':\Windows\System32\regsvr32.exe'
        TargetObject|endswith: '\InProcServer32\(Default)'
    condition: selection and not 1 of filter_main_*
falsepositives:
    - Some installed utilities (i.e. OneDrive) may serve new COM objects at user-level
level: medium
