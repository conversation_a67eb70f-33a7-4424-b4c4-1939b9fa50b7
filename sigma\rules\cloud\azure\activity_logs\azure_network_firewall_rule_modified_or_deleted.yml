title: Azure Firewall Rule Configuration Modified or Deleted
id: 2a7d64cf-81fa-4daf-ab1b-ab80b789c067
status: test
description: Identifies when a Firewall Rule Configuration is Modified or Deleted.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations
author: <PERSON> @austinsonger
date: 2021-08-08
modified: 2022-08-23
tags:
    - attack.impact
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName:
            - MICROSOFT.NETWORK/FIREWALLPOLICIES/RULECOLLECTIONGROUPS/WRITE
            - MICROSOFT.NETWORK/FIREWALLPOLICIES/RULECOLLECTIONGROUPS/DELETE
            - MICROSOFT.NETWORK/FIREWALLPOLICIES/RULEGROUPS/WRITE
            - MICROSOFT.NETWORK/FIREWALLPOLICIES/RULEGROUPS/DELETE
    condition: selection
falsepositives:
    - Firewall Rule Configuration being modified or deleted may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Firewall Rule Configuration modified or deleted from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
