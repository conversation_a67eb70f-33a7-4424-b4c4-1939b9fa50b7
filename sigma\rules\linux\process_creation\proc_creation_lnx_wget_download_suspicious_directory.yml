title: Download File To Potentially Suspicious Directory Via Wget
id: cf610c15-ed71-46e1-bdf8-2bd1a99de6c4
status: test
description: Detects the use of wget to download content to a suspicious directory
references:
    - https://blogs.jpcert.or.jp/en/2023/05/gobrat.html
    - https://jstnk9.github.io/jstnk9/research/GobRAT-Malware/
    - https://www.virustotal.com/gui/file/****************************************************************/detection
    - https://www.virustotal.com/gui/file/****************************************************************/detection
author: <PERSON><PERSON><PERSON>, @Joseliyo_Jstnk
date: 2023-06-02
tags:
    - attack.command-and-control
    - attack.t1105
logsource:
    category: process_creation
    product: linux
detection:
    selection_img:
        Image|endswith: '/wget'
    selection_output:
        - CommandLine|re: '\s-O\s' # We use regex to ensure a case sensitive argument detection
        - CommandLine|contains: '--output-document'
    selection_path:
        CommandLine|contains: '/tmp/'
    condition: all of selection_*
falsepositives:
    - Unknown
level: medium
