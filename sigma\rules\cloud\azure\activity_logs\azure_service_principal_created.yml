title: Azure Service Principal Created
id: 0ddcff6d-d262-40b0-804b-80eb592de8e3
status: test
description: Identifies when a service principal is created in Azure.
references:
    - https://learn.microsoft.com/en-us/entra/identity/monitoring-health/reference-audit-activities#application-proxy
author: <PERSON> @austinsonger
date: 2021-09-02
modified: 2022-10-09
tags:
    - attack.defense-evasion
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        properties.message: 'Add service principal'
    condition: selection
falsepositives:
    - Service principal being created may be performed by a system administrator.
    - Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - Service principal created from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
