title: Antivirus Password Dumper Detection
id: 78cc2dd2-7d20-4d32-93ff-057084c38b93
status: stable
description: |
    Detects a highly relevant Antivirus alert that reports a password dumper.
    This event must not be ignored just because the AV has blocked the malware but investigate, how it came there in the first place.
references:
    - https://www.nextron-systems.com/?s=antivirus
    - https://www.virustotal.com/gui/file/****************************************************************
    - https://www.virustotal.com/gui/file/****************************************************************
author: <PERSON><PERSON><PERSON> (Nextron Systems), Arn<PERSON>
date: 2018-09-09
modified: 2024-11-02
tags:
    - attack.credential-access
    - attack.t1003
    - attack.t1558
    - attack.t1003.001
    - attack.t1003.002
logsource:
    category: antivirus
detection:
    selection:
        - Signature|startswith: 'PWS'
        - Signature|contains:
              - 'Certify'
              - 'DCSync'
              - 'DumpCreds'
              - 'DumpLsass'
              - 'DumpPert'
              - 'HTool/WCE'
              - 'Kekeo'
              - 'Lazagne'
              - 'LsassDump'
              - 'Mimikatz'
              - 'MultiDump'
              - 'Nanodump'
              - 'NativeDump'
              - 'Outflank'
              - 'PShlSpy'
              - 'PSWTool'
              - 'PWCrack'
              - 'PWDump'
              - 'PWS.'
              - 'PWSX'
              - 'pypykatz'
              - 'Rubeus'
              - 'SafetyKatz'
              - 'SecurityTool'
              - 'SharpChrome'
              - 'SharpDPAPI'
              - 'SharpDump'
              - 'SharpKatz'
              - 'SharpS.' # Sharpsploit, e.g. 530ea2ff9049f5dfdfa0a2e9c27c2e3c0685eb6cbdf85370c20a7bfae49f592d
              - 'ShpKatz'
              - 'TrickDump'
    condition: selection
falsepositives:
    - Unlikely
level: critical
