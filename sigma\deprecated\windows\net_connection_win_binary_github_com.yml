title: Microsoft Binary Github Communication
id: 635dbb88-67b3-4b41-9ea5-a3af2dd88153
status: deprecated
description: Detects an executable in the Windows folder accessing github.com
references:
    - https://twitter.com/M_haggis/status/900741347035889665
    - https://twitter.com/M_haggis/status/1032799638213066752
    - https://github.com/EmpireProject/Empire/blob/e37fb2eef8ff8f5a0a689f1589f424906fe13055/data/module_source/exfil/Invoke-ExfilDataToGitHub.ps1
author: <PERSON> (idea), <PERSON><PERSON><PERSON> (Nextron Systems)
date: 2017/08/24
modified: 2023/04/18
tags:
    - attack.command_and_control
    - attack.t1105
    - attack.exfiltration
    - attack.t1567.001
logsource:
    category: network_connection
    product: windows
detection:
    selection:
        Initiated: 'true'
        DestinationHostname|endswith:
            - '.github.com'
            - '.githubusercontent.com'
        Image|startswith: 'C:\Windows\'
    condition: selection
falsepositives:
    - Unknown
    - '@subTee in your network'
level: high
