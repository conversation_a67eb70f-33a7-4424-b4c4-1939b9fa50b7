title: Added Credentials to Existing Application
id: cbb67ecc-fb70-4467-9350-c910bdf7c628
status: test
description: Detects when a new credential is added to an existing application. Any additional credentials added outside of expected processes could be a malicious actor using those credentials.
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#application-credentials
author: <PERSON> '@markmorow', <PERSON> '@baileybercik'
date: 2022-05-26
modified: 2025-07-18
tags:
    - attack.t1098.001
    - attack.persistence
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message:
            - Update application – Certificates and secrets management
            - Update Service principal/Update Application
    condition: selection
falsepositives:
    - When credentials are added/removed as part of the normal working hours/workflows
level: high
