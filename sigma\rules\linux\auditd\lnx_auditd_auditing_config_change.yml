title: Auditing Configuration Changes on Linux Host
id: 977ef627-4539-4875-adf4-ed8f780c4922
status: test
description: Detect changes in auditd configuration files
references:
    - https://github.com/Neo23x0/auditd/blob/master/audit.rules
    - Self Experience
author: <PERSON>, oscd.community
date: 2019-10-25
modified: 2021-11-27
tags:
    - attack.defense-evasion
    - attack.t1562.006
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: PATH
        name:
            - /etc/audit/*
            - /etc/libaudit.conf
            - /etc/audisp/*
    condition: selection
fields:
    - exe
    - comm
    - key
falsepositives:
    - Legitimate administrative activity
level: high
