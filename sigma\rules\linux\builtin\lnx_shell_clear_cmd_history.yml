title: Linux Command History Tampering
id: fdc88d25-96fb-4b7c-9633-c0e417fdbd4e
status: test
description: |
    Detects commands that try to clear or tamper with the Linux command history.
    This technique is used by threat actors in order to evade defenses and execute commands without them being recorded in files such as "bash_history" or "zsh_history".
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1070.003/T1070.003.md
    - https://www.hackers-arise.com/post/2016/06/20/covering-your-bash-shell-tracks-antiforensics
    - https://www.cadosecurity.com/spinning-yarn-a-new-linux-malware-campaign-targets-docker-apache-hadoop-redis-and-confluence/
author: <PERSON>
date: 2019-03-24
modified: 2024-04-17
tags:
    - attack.defense-evasion
    - attack.t1070.003
# Example config for this one (place it in .bash_profile):
#  (is_empty=false; inotifywait -m .bash_history | while read file; do if [ $(wc -l <.bash_history) -lt 1  ]; then if  [ "$is_empty" = false ]; then logger -i -p local5.info -t empty_bash_history "$USER : ~/.bash_history is empty "; is_empty=true; fi; else is_empty=false;  fi;  done ) &
#  It monitors the size of .bash_history and log the words "empty_bash_history" whenever a previously not empty bash_history becomes empty
#  We define an empty file as a document with 0 or 1 lines (it can be a line with only one space character for example)
#  It has two advantages over the version suggested by Patrick Bareiss  :
#    - it is not relative to the exact command used to clear .bash_history : for instance Caldera uses "> .bash_history" to clear the history and this is not one the commands listed here. We can't be exhaustive for all the possibilities !
#    - the method suggested by Patrick Bareiss logs all the commands entered directly in a bash shell. therefore it may miss some events (for instance it doesn't log the commands launched from a Caldera agent). Here if .bash_history is cleared, it will always be detected
logsource:
    product: linux
detection:
    keywords:
        - 'cat /dev/null >*sh_history'
        - 'cat /dev/zero >*sh_history'
        - 'chattr +i*sh_history'
        - 'echo "" >*sh_history'
        - 'empty_bash_history'
        - 'export HISTFILESIZE=0'
        - 'history -c'
        - 'history -w'
        - 'ln -sf /dev/null *sh_history'
        - 'ln -sf /dev/zero *sh_history'
        - 'rm *sh_history'
        - 'shopt -ou history'
        - 'shopt -uo history'
        - 'shred *sh_history'
        - 'truncate -s0 *sh_history'
        # - 'unset HISTFILE'  # prone to false positives
    condition: keywords
falsepositives:
    - Unknown
level: high
