title: Remote Schedule Task Lateral Movement via SASec
id: aff229ab-f8cd-447b-b215-084d11e79eb0
status: test
description: Detects remote RPC calls to create or execute a scheduled task via SASec
references:
    - https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-tsch/d1058a28-7e02-4948-8b8d-4a347fa64931
    - https://github.com/jsecurity101/MSRPC-to-ATTACK/blob/ddd4608fe8684fcf2fcf9b48c5f0b3c28097f8a3/documents/MS-TSCH.md
    - https://github.com/zeronetworks/rpcfirewall
    - https://zeronetworks.com/blog/stopping-lateral-movement-via-the-rpc-firewall/
author: <PERSON><PERSON>, De<PERSON>
date: 2022-01-01
tags:
    - attack.lateral-movement
    - attack.execution
    - attack.persistence
    - attack.t1053
    - attack.t1053.002
logsource:
    product: rpc_firewall
    category: application
    definition: 'Requirements: install and apply the RPC Firewall to all processes with "audit:true action:block uuid:378e52b0-c0a9-11cf-822d-00aa0051e40f"'
detection:
    selection:
        EventLog: RPCFW
        EventID: 3
        InterfaceUuid: 378e52b0-c0a9-11cf-822d-00aa0051e40f
        OpNum:
            - 0
            - 1
    condition: selection
falsepositives:
    - Unknown
level: high
