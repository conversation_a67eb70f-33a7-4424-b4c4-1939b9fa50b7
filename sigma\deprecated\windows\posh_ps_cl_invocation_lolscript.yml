title: Execution via CL_Invocation.ps1 - Powershell
id: 4cd29327-685a-460e-9dac-c3ab96e549dc
status: deprecated
description: Detects Execution via SyncInvoke in CL_Invocation.ps1 module
references:
    - https://lolbas-project.github.io/lolbas/Scripts/Cl_invocation/
    - https://twitter.com/bohops/status/948061991012327424
author: oscd.community, <PERSON>
date: 2020/10/14
modified: 2023/08/17
tags:
    - attack.defense_evasion
    - attack.t1216
logsource:
    product: windows
    category: ps_script
    definition: 'Requirements: Script Block Logging must be enabled'
detection:
    selection:
        ScriptBlockText|contains|all:
            - 'CL_Invocation.ps1'
            - 'SyncInvoke'
    condition: selection
falsepositives:
    - Unknown
level: high
