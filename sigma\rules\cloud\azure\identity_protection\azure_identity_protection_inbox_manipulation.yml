title: Suspicious Inbox Manipulation Rules
id: ceb55fd0-726e-4656-bf4e-b585b7f7d572
status: test
description: Detects suspicious rules that delete or move messages or folders are set on a user's inbox.
references:
    - https://learn.microsoft.com/en-us/entra/id-protection/concept-identity-protection-risks#suspicious-inbox-manipulation-rules
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-user-accounts#unusual-sign-ins
author: <PERSON> '@markmorow', <PERSON>, '@gleeiamglo'
date: 2023-09-03
tags:
    - attack.t1140
    - attack.defense-evasion
logsource:
    product: azure
    service: riskdetection
detection:
    selection:
        riskEventType: 'mcasSuspiciousInboxManipulationRules'
    condition: selection
falsepositives:
    - Actual mailbox rules that are moving items based on their workflow.
level: high
