title: Linux Network Service Scanning - Auditd
id: 3761e026-f259-44e6-8826-719ed8079408
related:
    - id: 3e102cd9-a70d-4a7a-9508-403963092f31
      type: derived
status: test
description: Detects enumeration of local or remote network services.
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1046/T1046.md
author: <PERSON>, oscd.community
date: 2020-10-21
modified: 2023-09-26
tags:
    - attack.discovery
    - attack.t1046
logsource:
    product: linux
    service: auditd
    definition: 'Configure these rules https://github.com/Neo23x0/auditd/blob/e181243a7c708e9d579557d6f80e0ed3d3483b89/audit.rules#L182-L183'
detection:
    selection:
        type: 'SYSCALL'
        exe|endswith:
            - '/telnet'
            - '/nmap'
            - '/netcat'
            - '/nc'
            - '/ncat'
            - '/nc.openbsd'
        key: 'network_connect_4'
    condition: selection
falsepositives:
    - Legitimate administration activities
level: low
