title: OMIGOD SCX RunAsProvider ExecuteShellCommand - Auditd
id: 045b5f9c-49f7-4419-a236-9854fb3c827a
status: unsupported # This rule requires correlations. See https://github.com/SigmaHQ/sigma/discussions/4440#discussioncomment-7070862 and https://user-images.githubusercontent.com/9653181/133756156-4fb9c2b1-aa65-4380-957b-72170de36fc4.png
description: |
    Rule to detect the use of the SCX RunAsProvider Invoke_ExecuteShellCommand to execute any UNIX/Linux command using the /bin/sh shell.
    SCXcore, started as the Microsoft Operations Manager UNIX/Linux Agent, is now used in a host of products including Microsoft Operations Manager.
    Microsoft Azure, and Microsoft Operations Management Suite.
references:
    - https://www.wiz.io/blog/omigod-critical-vulnerabilities-in-omi-azure
    - https://github.com/Azure/Azure-Sentinel/pull/3059
author: <PERSON> (Cyb3rWard0g), <PERSON><PERSON> (Open Threat Research)
date: 2021-09-17
modified: 2024-09-02
tags:
    - attack.privilege-escalation
    - attack.initial-access
    - attack.execution
    - attack.t1068
    - attack.t1190
    - attack.t1203
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'SYSCALL'
        syscall: 'execve'
        uid: 0
        cwd: '/var/opt/microsoft/scx/tmp'
        comm: 'sh'
    condition: selection
falsepositives:
    - Legitimate use of SCX RunAsProvider Invoke_ExecuteShellCommand.
level: high
