title: Suspicious CLR Logs Creation
id: e4b63079-6198-405c-abd7-3fe8b0ce3263
status: deprecated
description: Detects suspicious .NET assembly executions. Could detect using Cobalt Strike's command execute-assembly.
references:
    - https://web.archive.org/web/20230329154538/https://blog.menasec.net/2019/07/interesting-difr-traces-of-net-clr.html
    - https://bohops.com/2021/03/16/investigating-net-clr-usage-log-tampering-techniques-for-edr-evasion/
    - https://github.com/olafhartong/sysmon-modular/blob/5e5f6d90819a7f35eec0aba08021d0d201bb9055/11_file_create/include_dotnet.xml
author: omkar72, oscd.community, Wojciech Lesicki
date: 2020/10/12
modified: 2023/01/05
tags:
    - attack.execution
    - attack.defense_evasion
    - attack.t1059.001
    - attack.t1218
logsource:
    category: file_event
    product: windows
    definition: Check your sysmon configuration for monitoring UsageLogs folder. In SwiftOnSecurity configuration we have that thanks @SBousseaden
detection:
    selection:
        TargetFilename|contains|all:
            - '\AppData\Local\Microsoft\CLR'
            - '\UsageLogs\'
        TargetFilename|contains:
            - 'mshta'
            - 'cscript'
            - 'wscript'
            - 'regsvr32'
            - 'wmic'
            - 'rundll32'
            - 'svchost'
    condition: selection
falsepositives:
    - Rundll32.exe with zzzzInvokeManagedCustomActionOutOfProc in command line and msiexec.exe as parent process - https://twitter.com/SBousseaden/status/1388064061087260675
level: high
