title: AzureHound PowerShell Commands
id: 83083ac6-1816-4e76-97d7-59af9a9ae46e
status: deprecated
description: Detects the execution of AzureHound in PowerShell, a tool to gather data from Azure for BloodHound
references:
    - https://github.com/BloodHoundAD/BloodHound/blob/0927441f67161cc6dc08a53c63ceb8e333f55874/Collectors/AzureHound.ps1
    - https://bloodhound.readthedocs.io/en/latest/data-collection/azurehound.html
author: <PERSON> (@austinsonger)
date: 2021/10/23
modified: 2023/01/02
tags:
    - attack.discovery
    - attack.t1482
    - attack.t1087
    - attack.t1087.001
    - attack.t1087.002
    - attack.t1069.001
    - attack.t1069.002
    - attack.t1069
logsource:
    product: windows
    category: ps_script
    definition: Script Block Logging must be enabled
detection:
    selection:
        ScriptBlockText|contains: Invoke-AzureHound
    condition: selection
falsepositives:
    - Unknown
level: high
