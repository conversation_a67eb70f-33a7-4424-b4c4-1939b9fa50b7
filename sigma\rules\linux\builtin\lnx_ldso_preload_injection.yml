title: Code Injection by ld.so Preload
id: 7e3c4651-c347-40c4-b1d4-d48590fdf684
status: test
description: Detects the ld.so preload persistence file. See `man ld.so` for more information.
references:
    - https://man7.org/linux/man-pages/man8/ld.so.8.html
author: <PERSON> (Nextron Systems)
date: 2021-05-05
modified: 2022-10-09
tags:
    - attack.persistence
    - attack.privilege-escalation
    - attack.t1574.006
logsource:
    product: linux
detection:
    keywords:
        - '/etc/ld.so.preload'
    condition: keywords
falsepositives:
    - Rare temporary workaround for library misconfiguration
level: high
