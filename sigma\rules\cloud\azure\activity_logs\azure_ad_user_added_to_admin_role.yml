title: User Added to an Administrator's Azure AD Role
id: ebbeb024-5b1d-4e16-9c0c-917f86c708a7
status: test
description: User Added to an Administrator's Azure AD Role
references:
    - https://m365internals.com/2021/07/13/what-ive-learned-from-doing-a-year-of-cloud-forensics-in-azure-ad/
author: <PERSON><PERSON><PERSON>, @MetallicHack
date: 2021-10-04
modified: 2022-10-09
tags:
    - attack.persistence
    - attack.privilege-escalation
    - attack.t1098.003
    - attack.t1078
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        Operation: 'Add member to role.'
        Workload: 'AzureActiveDirectory'
        ModifiedProperties{}.NewValue|endswith:
            - 'Admins'
            - 'Administrator'
    condition: selection
falsepositives:
    - PIM (Privileged Identity Management) generates this event each time 'eligible role' is enabled.
level: medium
