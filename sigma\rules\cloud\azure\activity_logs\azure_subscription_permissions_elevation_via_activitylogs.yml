title: Azure Subscription Permission Elevation Via ActivityLogs
id: 09438caa-07b1-4870-8405-1dbafe3dad95
status: test
description: |
    Detects when a user has been elevated to manage all Azure Subscriptions.
    This change should be investigated immediately if it isn't planned.
    This setting could allow an attacker access to Azure subscriptions in your environment.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations#microsoftauthorization
author: <PERSON> @austinsonger
date: 2021-11-26
modified: 2022-08-23
tags:
    - attack.initial-access
    - attack.t1078.004
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName: MICROSOFT.AUTHORIZATION/ELEVATEACCESS/ACTION
    condition: selection
falsepositives:
    - If this was approved by System Administrator.
level: high
