title: PowerShell Scripts Run by a Services
id: 46deb5e1-28c9-4905-b2df-51cdcc9e6073
related:
    - id: a2e5019d-a658-4c6a-92bf-7197b54e2cae
      type: derived
status: deprecated
description: Detects powershell script installed as a Service
references:
    - https://speakerdeck.com/heirhabarov/hunting-for-powershell-abuse
author: oscd.community, <PERSON>
date: 2020/10/06
modified: 2023/12/11
tags:
    - attack.execution
    - attack.t1569.002
logsource:
    product: windows
    category: driver_load
detection:
    selection:
        ImageLoaded|contains:
            - 'powershell'
            - 'pwsh'
    condition: selection
falsepositives:
    - Unknown
level: high
