title: Bitbucket User Permissions Export Attempt
id: 87cc6698-3e07-4ba2-9b43-a85a73e151e2
status: test
description: Detects user permission data export attempt.
references:
    - https://confluence.atlassian.com/bitbucketserver/audit-log-events-776640423.html
    - https://confluence.atlassian.com/bitbucketserver/users-and-groups-776640439.html
author: <PERSON> (@faisalusuf)
date: 2024-02-25
tags:
    - attack.reconnaissance
    - attack.collection
    - attack.discovery
    - attack.t1213
    - attack.t1082
    - attack.t1591.004
logsource:
    product: bitbucket
    service: audit
    definition: 'Requirements: "Advance" log level is required to receive these audit events.'
detection:
    selection:
        auditType.category: 'Users and groups'
        auditType.action:
            - 'User details export failed'
            - 'User details export started'
            - 'User details exported'
    condition: selection
falsepositives:
    - Legitimate user activity.
level: medium
