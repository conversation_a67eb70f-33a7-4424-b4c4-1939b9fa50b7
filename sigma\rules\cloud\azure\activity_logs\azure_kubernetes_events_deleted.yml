title: Azure Kubernetes Events Deleted
id: 225d8b09-e714-479c-a0e4-55e6f29adf35
status: test
description: Detects when Events are deleted in Azure Kubernetes. An adversary may delete events in Azure Kubernetes in an attempt to evade detection.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations#microsoftkubernetes
    - https://github.com/elastic/detection-rules/blob/da3852b681cf1a33898b1535892eab1f3a76177a/rules/integrations/azure/defense_evasion_kubernetes_events_deleted.toml
author: <PERSON> @austinsonger
date: 2021-07-24
modified: 2022-08-23
tags:
    - attack.defense-evasion
    - attack.t1562
    - attack.t1562.001
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName: MICROSOFT.KUBERNETES/CONNECTEDCLUSTERS/EVENTS.K8S.IO/EVENTS/DELETE
    condition: selection
falsepositives:
    - Event deletions may be done by a system or network administrator. Verify whether the username, hostname, and/or resource name should be making changes in your environment. Events deletions from unfamiliar users or hosts should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
