title: Guest Users Invited To Tenant By Non Approved Inviters
id: 4ad97bf5-a514-41a4-abd3-4f3455ad4865
status: test
description: Detects guest users being invited to tenant by non-approved inviters
references:
    - https://learn.microsoft.com/en-gb/entra/architecture/security-operations-user-accounts#monitoring-external-user-sign-ins
author: <PERSON><PERSON><PERSON><PERSON><PERSON>, '@dudders1'
date: 2022-07-28
tags:
    - attack.initial-access
    - attack.t1078
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        Category: 'UserManagement'
        OperationName: 'Invite external user'
    filter:
        InitiatedBy|contains: '<approved guest inviter use OR for multiple>'
    condition: selection and not filter
falsepositives:
    - If this was approved by System Administrator.
level: medium
