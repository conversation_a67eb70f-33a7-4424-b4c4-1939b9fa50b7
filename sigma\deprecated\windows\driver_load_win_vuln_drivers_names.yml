title: Vulnerable Driver Load By Name
id: 39b64854-5497-4b57-a448-40977b8c9679
related:
    - id: 7aaaf4b8-e47c-4295-92ee-6ed40a6f60c8
      type: derived
status: deprecated
description: Detects the load of known vulnerable drivers via their names only.
references:
    - https://loldrivers.io/
author: <PERSON><PERSON><PERSON><PERSON> (Nextron Systems)
date: 2022/10/03
modified: 2023/09/03
tags:
    - attack.privilege_escalation
    - attack.t1543.003
    - attack.t1068
logsource:
    product: windows
    category: driver_load
detection:
    selection:
        ImageLoaded|endswith:
            - '\reddriver.sys'
            - '\mhyprot2.sys'
            - '\hwos2ec7x64.sys'
            - '\asrdrv103.sys'
            - '\e29f6311ae87542b3d693c1f38e4e3ad.sys'
            - '\gvcidrv64.sys'
            - '\spwizimgvt.sys'
            - '\hwos2ec10x64.sys'
            - '\e939448b28a4edc81f1f974cebf6e7d2.sys'
            - '\phymemx64.sys'
            - '\dh_kernel.sys'
            - '\bs_def.sys'
            - '\nbiolib_x64.sys'
            - '\viraglt64.sys'
            - '\ntiolib.sys'
            - '\paniox64.sys'
            - '\libnicm.sys'
            - '\phymem64.sys'
            - '\fiddrv.sys'
            - '\cpuz141.sys'
            - '\yyprotect64.sys'
            - '\daxin_blank3.sys'
            - '\aswarpot.sys'
            - '\t8.sys'
            - '\driver7-x86-withoutdbg.sys'
            - '\dcr.sys'
            - '\b3.sys'
            - '\asupio.sys'
            - '\blackbonedrv10.sys'
            - '\rzpnk.sys'
            - '\iomem64.sys'
            - '\kfeco11x64.sys'
            - '\t.sys'
            - '\wantd.sys'
            - '\mimikatz.sys'
            - '\wantd_4.sys'
            - '\chaos-rootkit.sys'
            - '\mhyprot.sys'
            - '\nlslexicons0024uvn.sys'
            - '\piddrv64.sys'
            - '\aswvmm.sys'
            - '\superbmc.sys'
            - '\kprocesshacker.sys'
            - '\lmiinfo.sys'
            - '\jokercontroller.sys'
            - '\blackbone.sys'
            - '\fur.sys'
            - '\vboxmousent.sys'
            - '\mapmom.sys'
            - '\windows-xp-64.sys'
            - '\d3.sys'
            - '\inpout32.sys'
            - '\tfbfs3ped.sys'
            - '\etdsupp.sys'
            - '\asmmap64.sys'
            - '\lurker.sys'
            - '\alsysio64.sys'
            - '\ntiolib_x64.sys'
            - '\asas.sys'
            - '\vproeventmonitor.sys'
            - '\dbutil_2_3.sys'
            - '\malicious.sys'
            - '\cpupress.sys'
            - '\netfilter2.sys'
            - '\wintapix.sys'
            - '\mhyprotnap.sys'
            - '\ktes.sys'
            - '\titidrv.sys'
            - '\rtcore64.sys'
            - '\physmem.sys'
            - '\d.sys'
            - '\asrdrv106.sys'
            - '\winiodrv.sys'
            - '\phlashnt.sys'
            - '\sfdrvx64.sys'
            - '\ene.sys'
            - '\nqrmq.sys'
            - '\phydmaccx86.sys'
            - '\fd3b7234419fafc9bdd533f48896ed73_b816c5cd.sys'
            - '\magdrvamd64.sys'
            - '\a26363e7b02b13f2b8d697abb90cd5c3.sys'
            - '\amdryzenmasterdriver.sys'
            - '\amigendrv64.sys'
            - '\d2.sys'
            - '\agent64.sys'
            - '\bs_rcio64.sys'
            - '\goad.sys'
            - '\bsmi.sys'
            - '\nvflsh64.sys'
            - '\gametersafe.sys'
            - '\ndislan.sys'
            - '\bw.sys'
            - '\directio32.sys'
            - '\asrsmartconnectdrv.sys'
            - '\ktgn.sys'
            - '\eneio64.sys'
            - '\amp.sys'
            - '\gdrv.sys'
            - '\tmel.sys'
            - '\nstr.sys'
            - '\winring0.sys'
            - '\fiddrv64.sys'
            - '\tmcomm.sys'
            - '\daxin_blank2.sys'
            - '\poortry2.sys'
            - '\bsmemx64.sys'
            - '\asio.sys'
            - '\gmer64.sys'
            - '\panio.sys'
            - '\ucorew64.sys'
            - '\atszio64.sys'
            - '\nt2.sys'
            - '\pciecubed.sys'
            - '\nvflsh32.sys'
            - '\ssport.sys'
            - '\wcpu.sys'
            - '\winio64.sys'
            - '\msio64.sys'
            - '\black.sys'
            - '\nicm.sys'
            - '\daxin_blank1.sys'
            - '\my.sys'
            - '\tgsafe.sys'
            - '\dbk64.sys'
            - '\proxydrv.sys'
            - '\1fc7aeeff3ab19004d2e53eae8160ab1.sys'
            - '\capcom.sys'
            - '\asio32.sys'
            - '\proxy32.sys'
            - '\asrdrv102.sys'
            - '\vboxguest.sys'
            - '\vboxtap.sys'
            - '\daxin_blank.sys'
            - '\poortry.sys'
            - '\ntbios.sys'
            - '\glckio2.sys'
            - '\dbutildrv2.sys'
            - '\kfeco10x64.sys'
            - '\lenovodiagnosticsdriver.sys'
            - '\netfilter.sys'
            - '\corsairllaccess64.sys'
            - '\semav6msr.sys'
            - '\bs_rciow1064.sys'
            - '\vboxusbmon.sys'
            - '\nodedriver.sys'
            - '\iobitunlocker.sys'
            - '\smep_namco.sys'
            - '\asio64.sys'
            - '\xjokercontroller.sys'
            - '\irec.sys'
            - '\asribdrv.sys'
            - '\mhyprot3.sys'
            - '\daxin_blank6.sys'
            - '\fidpcidrv.sys'
            - '\bandai.sys'
            - '\procexp.sys'
            - '\daxin_blank5.sys'
            - '\daxin_blank4.sys'
            - '\bedaisy.sys'
            - '\asrdrv10.sys'
            - '\bwrsh.sys'
            - '\eio.sys'
            - '\winio64a.sys'
            - '\citmdrv_ia64.sys'
            - '\7.sys'
            - '\b.sys'
            - '\bwrs.sys'
            - '\nt3.sys'
            - '\wiseunlo.sys'
            - '\ncpl.sys'
            - '\ctiio64.sys'
            - '\hw.sys'
            - '\asromgdrv.sys'
            - '\bs_hwmio64.sys'
            - '\lgdatacatcher.sys'
            - '\rtkio.sys'
            - '\winio32.sys'
            - '\phydmaccx64.sys'
            - '\mtcbsv64.sys'
            - '\ni.sys'
            - '\b4.sys'
            - '\directio64.sys'
            - '\vboxdrv.sys'
            - '\nvflash.sys'
            - '\hpportiox64.sys'
            - '\bs_i2c64.sys'
            - '\iomap64.sys'
            - '\vboxusb.sys'
            - '\msqpq.sys'
            - '\sysinfo.sys'
            - '\mhyprotect.sys'
            - '\naldrv.sys'
            - '\lgdcatcher.sys'
            - '\echo_driver.sys'
            - '\otipcibus.sys'
            - '\testbone.sys'
            - '\lctka.sys'
            - '\wyproxy64.sys'
            - '\pchunter.sys'
            - '\amdpowerprofiler.sys'
            - '\wantd_3.sys'
            - '\test2.sys'
            - '\rtcoremini64.sys'
            - '\d4.sys'
            - '\piddrv.sys'
            - '\panmonflt.sys'
            - '\windows8-10-32.sys'
            - '\wantd_5.sys'
            - '\mjj0ge.sys'
            - '\kt2.sys'
            - '\rtkiow8x64.sys'
            - '\nstrwsk.sys'
            - '\msio32.sys'
            - '\ktmutil7odm.sys'
            - '\hwrwdrv.sys'
            - '\nchgbios2x64.sys'
            - '\bs_hwmio64_w10.sys'
            - '\mydrivers.sys'
            - '\t7.sys'
            - '\wantd_6.sys'
            - '\sandra.sys'
            - '\atillk64.sys'
            - '\cpuz.sys'
            - '\netproxydriver.sys'
            - '\protects.sys'
            - '\asrrapidstartdrv.sys'
            - '\dh_kernel_10.sys'
            - '\ef0e1725aaf0c6c972593f860531a2ea.sys'
            - '\enetechio64.sys'
            - '\citmdrv_amd64.sys'
            - '\iqvw64e.sys'
            - '\bsmixp64.sys'
            - '\bs_i2cio.sys'
            - '\prokiller64.sys'
            - '\netflt.sys'
            - '\4748696211bd56c2d93c21cab91e82a5.sys'
            - '\openlibsys.sys'
            - '\adv64drv.sys'
            - '\be6318413160e589080df02bb3ca6e6a.sys'
            - '\cupfixerx64.sys'
            - '\se64a.sys'
            - '\speedfan.sys'
            - '\a236e7d654cd932b7d11cb604629a2d0.sys'
            - '\winio32b.sys'
            - '\winio64b.sys'
            - '\sysdrv3s.sys'
            - '\lv561av.sys'
            - '\bs_def64.sys'
            - '\mlgbbiicaihflrnh.sys'
            - '\dbutil.sys'
            - '\834761775.sys'
            - '\kdriver.sys'
            - '\spf.sys'
            - '\dkrtk.sys'
            - '\bs_flash64.sys'
            - '\nt4.sys'
            - '\4.sys'
            - '\directio32_legacy.sys'
            - '\viragt64.sys'
            - '\hostnt.sys'
            - '\poortry1.sys'
            - '\c94f405c5929cfcccc8ad00b42c95083.sys'
            - '\b1.sys'
            - '\wantd_2.sys'
            - '\mhyprotrpg.sys'
            - '\nscm.sys'
            - '\smep_capcom.sys'
            - '\sense5ext.sys'
            - '\lha.sys'
            - '\atszio.sys'
            - '\amifldrv64.sys'
            - '\blacklotus_driver.sys'
            - '\asrautochkupddrv.sys'
            - '\cpuz_x64.sys'
            - '\asrautochkupddrv_1_0_32.sys'
            - '\bs_rcio.sys'
            - '\elbycdio.sys'
            - '\fidpcidrv64.sys'
            - '\elrawdsk.sys'
            - '\telephonuafy.sys'
            - '\rwdrv.sys'
            - '\lgcoretemp.sys'
            - '\segwindrvx64.sys'
            - '\windows7-32.sys'
            - '\asrsetupdrv103.sys'
            - '\hwinfo32.sys'
            - '\inpoutx64.sys'
            - '\asrdrv101.sys'
            - '\asupio64.sys'
            - '\monitor_win10_x64.sys'
            - '\msrhook.sys'
            - '\nt5.sys'
            - '\wfshbr64.sys'
            - '\driver7.sys'
            - '\sfdrvx32.sys'
            - '\asrdrv104.sys'
            - '\gameink.sys'
            - '\hwinfo64i.sys'
            - '\bsmix64.sys'
            - '\winio32a.sys'
            - '\kbdcap64.sys'
            - '\5a4fe297c7d42539303137b6d75b150d.sys'
            - '\fairplaykd.sys'
            - '\a9df5964635ef8bd567ae487c3d214c4.sys'
            - '\fgme.sys'
            - '\skill.sys'
            - '\capcom2.sys'
            - '\typelibde.sys'
            - '\nt6.sys'
            - '\winio64c.sys'
            - '\driver7-x64.sys'
            - '\air_system10.sys'
            - '\panmonfltx64.sys'
            - '\ntbios_2.sys'
            - '\viragt.sys'
            - '\zam64.sys'
            - '\vmdrv.sys'
            - '\iqvw64.sys'
            - '\1.sys'
            - '\t3.sys'
            - '\2.sys'
            - '\gftkyj64.sys'
            - '\proxy64.sys'
            - '\kevp64.sys'
            - '\netfilterdrv.sys'
            - '\4118b86e490aed091b1a219dba45f332.sys'
            - '\6771b13a53b9c7449d4891e427735ea2.sys'
            - '\mimidrv.sys'
            - '\driver7-x86.sys'
            - '\windbg.sys'
            - '\80.sys'
            - '\directio.sys'
            - '\atomicredteamcapcom.sys'
            - '\81.sys'
            - '\full.sys'
            - '\asrdrv.sys'
            - '\kapchelper_x64.sys'
            - '\c.sys'
            - '\winflash64.sys'
            - '\amsdk.sys'
    condition: selection
falsepositives:
    - False positives may occur if one of the vulnerable driver names mentioned above didn't change its name between versions. So always make sure that the driver being loaded is the legitimate one and the non-vulnerable version.
    - If you experience a lot of FP you could comment the driver name or its exact known legitimate location (when possible)
level: low
