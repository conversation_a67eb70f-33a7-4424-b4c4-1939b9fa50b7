id,title,date,modified,level
867613fb-fa60-4497-a017-a82df74a172c,PowerShell Execution,2019-09-12,2021-11-05,medium
0d894093-71bc-43c3-8c4d-ecfc28dcf5d9,Mimikatz Detection LSASS Access,2017-10-18,2022-04-11,high
3d304fda-78aa-43ed-975c-d740798a49c1,Suspicious PowerShell Invocations - Generic,2017-03-12,2022-04-11,high
56a8189f-11b2-48c8-8ca7-c54b03c2fbf7,Suspicious Esentutl Use,2020-05-23,2022-04-11,high
65531a81-a694-4e31-ae04-f8ba5bc33759,Suspicious PowerShell Download,2017-03-05,2022-04-11,medium
9f7aa113-9da6-4a8d-907c-5f1a4b908299,SyncAppvPublishingServer Execution to Bypass Powershell Restriction,2020-10-05,2022-04-11,medium
a0d63692-a531-4912-ad39-4393325b2a9c,<PERSON><PERSON> Execution,2021-05-10,2022-04-11,high
b932b60f-fdda-4d53-8eda-a170c1d97bbd,Activity Related to NTDS.dit Domain Hash Retrieval,2019-01-16,2022-04-11,high
cb7286ba-f207-44ab-b9e6-760d82b84253,Rclone Execution via Command Line or PowerShell,2021-05-26,2022-04-11,high
fde7929d-8beb-4a4c-b922-be9974671667,SyncAppvPublishingServer Execution to Bypass Powershell Restriction,2020-10-05,2022-04-11,medium
17f878b8-9968-4578-b814-c4217fc5768c,Autorun Keys Modification,2019-10-25,2022-05-14,medium
29d31aee-30f4-4006-85a9-a4a02d65306c,Lateral Movement Indicator ConDrv,2021-04-27,2022-05-14,low
98f4c75c-3089-44f3-b733-b327b9cd9c9d,Accessing Encrypted Credentials from Google Chrome Login Database,2021-12-20,2022-05-14,medium
a457f232-7df9-491d-898f-b5aabd2cbe2f,Windows Management Instrumentation DLL Loaded Via Microsoft Word,2019-12-26,2022-05-14,informational
db2110f3-479d-42a6-94fb-d35bc1e46492,CreateMiniDump Hacktool,2019-12-22,2022-05-14,high
2621b3a6-3840-4810-ac14-a02426086171,Winword.exe Loads Suspicious DLL,2020-10-09,2022-07-25,medium
bf6c39fc-e203-45b9-9538-05397c1b4f3f,Abusing Findstr for Defense Evasion,2020-10-05,2022-10-12,medium
82a19e3a-2bfe-4a91-8c0d-5d4c98fbb719,Possible Applocker Bypass,2019-01-16,2022-11-03,low
dca91cfd-d7ab-4c66-8da7-ee57d487b35b,Process Start From Suspicious Folder,2022-02-11,2022-11-03,low
53c7cca0-2901-493a-95db-d00d6fcf0a37,Brute Force,2019-10-25,2022-11-04,medium
5f113a8f-8b61-41ca-b90f-d374fa7e4a39,Suspicious In-Memory Module Execution,2019-10-27,2022-11-17,low
f67dbfce-93bc-440d-86ad-a95ae8858c90,Suspicious Bitsadmin Job via PowerShell,2018-10-30,2022-11-21,high
9d1c72f5-43f0-4da5-9320-648cf2099dd0,Excel Proxy Executing Regsvr32 With Payload,2021-08-23,2022-12-02,high
c0e1c3d5-4381-4f18-8145-2583f06a1fe5,Excel Proxy Executing Regsvr32 With Payload Alternate,2021-08-23,2022-12-02,high
72671447-4352-4413-bb91-b85569687135,Nslookup PwSh Download Cradle,2022-09-06,2022-12-14,medium
3f07b9d1-2082-4c56-9277-613a621983cc,Accessing WinAPI in PowerShell for Credentials Dumping,2020-10-06,2022-12-18,high
e554f142-5cf3-4e55-ace9-a1b59e0def65,DCOM InternetExplorer.Application Iertutil DLL Hijack - Sysmon,2020-10-12,2022-12-18,critical
17eb8e57-9983-420d-ad8a-2c4976c22eb8,MavInject Process Injection,2018-12-12,2022-12-19,high
36c5146c-d127-4f85-8e21-01bf62355d5a,Invoke-Obfuscation Via Use Rundll32,2019-10-08,2022-12-30,high
6d3f1399-a81c-4409-aff3-1ecfe9330baf,PrintNightmare Powershell Exploitation,2021-08-09,2023-01-02,high
83083ac6-1816-4e76-97d7-59af9a9ae46e,AzureHound PowerShell Commands,2021-10-23,2023-01-02,high
a85cf4e3-56ee-4e79-adeb-789f8fb209a8,Indirect Command Exectuion via Forfiles,2022-10-17,2023-01-04,medium
fa47597e-90e9-41cd-ab72-c3b74cfb0d02,Indirect Command Execution,2019-10-24,2023-01-04,low
e4b63079-6198-405c-abd7-3fe8b0ce3263,Suspicious CLR Logs Creation,2020-10-12,2023-01-05,high
cd5c8085-4070-4e22-908d-a5b3342deb74,Suspicious Bitstransfer via PowerShell,2021-08-19,2023-01-10,medium
d178a2d7-129a-4ba4-8ee6-d6e1fecd5d20,Renamed PowerShell,2019-08-22,2023-01-18,high
d4d2574f-ac17-4d9e-b986-aeeae0dc8fe2,Renamed Rundll32.exe Execution,2022-06-08,2023-01-18,high
e31f89f7-36fb-4697-8ab6-48823708353b,Suspicious Cmd Execution via WMI,2022-09-27,2023-01-19,medium
bf7286e7-c0be-460b-a7e8-5b2e07ecc2f2,Netcat The Powershell Version - PowerShell Module,2021-07-21,2023-01-20,medium
47688f1b-9f51-4656-b013-3cc49a166a36,Base64 Encoded Listing of Shadowcopy,2022-03-01,2023-01-30,high
5b572dcf-254b-425c-a8c5-d9af6bea35a6,Potential Xor Encoded PowerShell Command,2022-07-06,2023-01-30,medium
fd6e2919-3936-40c9-99db-0aa922c356f7,Malicious Base64 Encoded Powershell Invoke Cmdlets,2022-05-31,2023-01-30,high
eeb66bbb-3dde-4582-815a-584aee9fe6d1,Correct Execution of Nltest.exe,2021-10-04,2023-02-02,high
0acaad27-9f02-4136-a243-c357202edd74,Ryuk Ransomware Command Line Activity,2019-08-06,2023-02-03,critical
4f927692-68b5-4267-871b-073c45f4f6fe,PowerShell AMSI Bypass Pattern,2022-11-04,2023-02-03,high
038cd51c-3ad8-41c5-ba8f-5d1c92f3cc1e,Registry Dump of SAM Creds and Secrets,2022-01-05,2023-02-04,high
04f5363a-6bca-42ff-be70-0d28bf629ead,Office Applications Spawning Wmi Cli Alternate,2021-08-23,2023-02-04,high
23daeb52-e6eb-493c-8607-c4f0246cb7d8,New Lolbin Process by Office Applications,2021-08-23,2023-02-04,high
518643ba-7d9c-4fa5-9f37-baed36059f6a,WMI Execution Via Office Process,2021-08-23,2023-02-04,medium
77815820-246c-47b8-9741-e0def3f57308,Domain Trust Discovery,2019-10-23,2023-02-04,medium
4d6c9da1-318b-4edf-bcea-b6c93fa98fd0,Credential Acquisition via Registry Hive Dumping,2022-10-04,2023-02-06,high
6545ce61-a1bd-4119-b9be-fcbee42c0cf3,Execute MSDT.EXE Using Diagcab File,2022-06-09,2023-02-06,high
9841b233-8df8-4ad7-9133-b0b4402a9014,Sysinternals SDelete Registry Keys,2020-05-02,2023-02-07,medium
09af397b-c5eb-4811-b2bb-08b3de464ebf,WMI Reconnaissance List Remote Services,2022-01-01,2023-02-14,medium
7b0666ad-3e38-4e3d-9bab-78b06de85f7b,Renamed PaExec Execution,2019-04-17,2023-02-14,medium
bc3cc333-48b9-467a-9d1f-d44ee594ef48,SCM DLL Sideload,2022-12-01,2023-02-14,medium
e42af9df-d90b-4306-b7fb-05c863847ebd,WMI Remote Command Execution,2022-03-13,2023-02-14,medium
fa4b21c9-0057-4493-b289-2556416ae4d7,Squirrel Lolbin,2019-11-12,2023-02-14,medium
e011a729-98a6-4139-b5c4-bf6f6dd8239a,Suspicious Certutil Command Usage,2019-01-16,2023-02-15,high
034affe8-6170-11ec-844f-0f78aa0c4d66,Mimikatz MemSSP Default Log File Creation,2021-12-20,2023-02-16,critical
7fe71fc9-de3b-432a-8d57-8c809efc10ab,New Service Creation,2019-10-21,2023-02-20,low
056a7ee1-4853-4e67-86a0-3fd9ceed7555,Invoke-Obfuscation RUNDLL LAUNCHER,2020-10-18,2023-02-21,medium
3ede524d-21cc-472d-a3ce-d21b568d8db7,PsExec Service Start,2018-03-13,2023-02-28,low
80167ada-7a12-41ed-b8e9-aa47195c66a1,Run Whoami as SYSTEM,2019-10-23,2023-02-28,high
fa91cc36-24c9-41ce-b3c8-3bbc3f2f67ba,PsExec Tool Execution,2017-06-12,2023-02-28,low
2c0d2d7b-30d6-4d14-9751-7b9113042ab9,Suspicious Characters in CommandLine,2022-04-27,2023-03-03,high
6783aa9e-0dc3-49d4-a94a-8b39c5fd700b,Stop Or Remove Antivirus Service,2021-07-07,2023-03-04,high
7fd4bb39-12d0-45ab-bb36-cebabc73dc7b,Suspicious Execution of Sc to Delete AV Services,2022-08-01,2023-03-04,high
a7a7e0e5-1d57-49df-9c58-9fe5bc0346a2,Renamed PsExec,2019-05-21,2023-03-04,high
1a70042a-6622-4a2b-8958-267625349abf,Run from a Zip File,2021-12-26,2023-03-05,medium
46591fae-7a4c-46ea-aec3-dff5e6d785dc,Root Certificate Installed,2020-10-10,2023-03-05,medium
eb87818d-db5d-49cc-a987-d5da331fbd90,Stop Windows Service,2019-10-23,2023-03-05,low
23250293-eed5-4c39-b57a-841c8933a57d,Visual Basic Script Execution,2022-01-02,2023-03-06,medium
344482e4-a477-436c-aa70-7536d18a48c7,Execution via MSSQL Xp_cmdshell Stored Procedure,2022-09-28,2023-03-06,high
00a4bacd-6db4-46d5-9258-a7d5ebff4003,Read and Execute a File Via Cmd.exe,2022-08-20,2023-03-07,medium
70e68156-6571-427b-a6e9-4476a173a9b6,Cmd Stream Redirection,2022-02-04,2023-03-07,medium
033fe7d6-66d1-4240-ac6b-28908009c71f,APT29,2018-12-04,2023-03-08,high
04d9079e-3905-4b70-ad37-6bdf11304965,CrackMapExecWin,2018-04-08,2023-03-08,critical
18739897-21b1-41da-8ee4-5b786915a676,GALLIUM Artefacts,2020-02-07,2023-03-09,high
0eb2107b-a596-422e-b123-b389d5594ed7,Hurricane Panda Activity,2019-03-04,2023-03-10,high
36222790-0d43-4fe8-86e4-674b27809543,DNS Tunnel Technique from MuddyWater,2020-06-04,2023-03-10,critical
4a12fa47-c735-4032-a214-6fab5b120670,Lazarus Activity Apr21,2021-04-20,2023-03-10,high
7b49c990-4a9a-4e65-ba95-47c9cc448f6e,Lazarus Loaders,2020-12-23,2023-03-10,critical
43f487f0-755f-4c2a-bce7-d6d2eec2fcf8,Suspicious Add Scheduled Task From User AppData Temp,2021-11-03,2023-03-14,high
d813d662-785b-42ca-8b4a-f7457d78d5a9,Suspicious Load of Advapi31.dll,2022-02-03,2023-03-15,informational
e74e15cc-c4b6-4c80-b7eb-dfe49feb7fe9,Edit of .bash_profile and .bashrc,2019-05-12,2023-03-23,medium
ba2a7c80-027b-460f-92e2-57d113897dbc,App Permissions Granted For Other APIs,2022-07-28,2023-03-29,medium
18cf6cf0-39b0-4c22-9593-e244bdc9a2d4,TA505 Dropper Load Pattern,2020-12-08,2023-04-05,critical
2d117e49-e626-4c7c-bd1f-c3c0147774c8,Potential PowerShell Base64 Encoded Shellcode,2018-11-17,2023-04-06,medium
635dbb88-67b3-4b41-9ea5-a3af2dd88153,Microsoft Binary Github Communication,2017-08-24,2023-04-18,high
6c939dfa-c710-4e12-a4dd-47e1f10e68e1,Domestic Kitten FurBall Malware Pattern,2021-02-08,2023-04-20,high
6355a919-2e97-4285-a673-74645566340d,Process Memory Dumped Via RdrLeakDiag.EXE,2022-01-04,2023-04-24,high
9cf01b6c-e723-4841-a868-6d7f8245ca6e,Group Modification Logging,2019-03-26,2023-04-26,low
410ad193-a728-4107-bc79-4419789fcbf8,Trickbot Malware Reconnaissance Activity,2019-12-28,2023-04-28,high
fce5f582-cc00-41e1-941a-c6fabf0fdb8c,Suspicious PowerShell Invocations - Specific,2017-03-05,2023-05-04,high
f016c716-754a-467f-a39e-63c06f773987,Suspicious Remote Thread Target,2022-08-25,2023-05-05,medium
65d2be45-8600-4042-b4c0-577a1ff8a60e,Application Whitelisting Bypass via DLL Loaded by odbcconf.exe,2019-10-25,2023-05-22,medium
8e2b24c9-4add-46a0-b4bb-0057b4e6187d,Regsvr32 Anomaly,2019-01-16,2023-05-26,high
fe6e002f-f244-4278-9263-20e4b593827f,Alternate PowerShell Hosts - Image,2019-09-12,2023-06-01,low
9e77ed63-2ecf-4c7b-b09d-640834882028,PsExec Pipes Artifacts,2020-05-10,2023-08-07,medium
39776c99-1c7b-4ba0-b5aa-641525eee1a4,Execution via CL_Mutexverifiers.ps1,2020-10-14,2023-08-17,high
4cd29327-685a-460e-9dac-c3ab96e549dc,Execution via CL_Invocation.ps1 - Powershell,2020-10-14,2023-08-17,high
4e8d5fd3-c959-441f-a941-f73d0cdcdca5,Abusing Windows Telemetry For Persistence - Registry,2020-09-29,2023-08-17,high
7c637634-c95d-4bbf-b26c-a82510874b34,Disable Microsoft Office Security Features,2021-06-08,2023-08-17,high
8a58209c-7ae6-4027-afb0-307a78e4589a,User Account Hidden By Registry,2022-08-20,2023-08-17,high
a166f74e-bf44-409d-b9ba-ea4b2dd8b3cd,Office Security Settings Changed,2020-05-22,2023-08-17,high
c81fe886-cac0-4913-a511-2822d72ff505,SilentProcessExit Monitor Registration,2021-02-26,2023-08-17,high
0c1ffcf9-efa9-436e-ab68-23a9496ebf5b,User Added To Admin Group - MacOS,2023-03-19,2023-08-22,medium
5b80cf53-3a46-4adc-960b-05ec19348d74,Wscript Execution from Non C Drive,2022-10-01,2023-08-29,medium
5e3d3601-0662-4af0-b1d2-36a05e90c40a,LSASS Memory Dump File Creation,2019-10-22,2023-08-29,high
39b64854-5497-4b57-a448-40977b8c9679,Vulnerable Driver Load By Name,2022-10-03,2023-09-03,low
21b23707-60d6-41bb-96e3-0f0481b0fed9,Vulnerable Dell BIOS Update Driver Load,2021-05-05,2023-09-12,high
7bcfeece-e5ed-4ff3-a5fb-2640d8cc8647,Vulnerable GIGABYTE Driver Load,2022-07-25,2023-09-12,high
7c676970-af4f-43c8-80af-ec9b49952852,Vulnerable AVAST Anti Rootkit Driver Load,2022-07-28,2023-09-12,high
9bacc538-d1b9-4d42-862e-469eafc05a41,Vulnerable HW Driver Load,2022-07-26,2023-09-12,high
ac683a42-877b-4ff8-91ac-69e94b0f70b4,Vulnerable Lenovo Driver Load,2022-11-10,2023-09-12,high
91bc09e7-674d-4cf5-8d86-ed5d8bdb95a6,Usage Of Malicious POORTRY Signed Driver,2022-12-16,2023-09-13,high
d7825193-b70a-48a4-b992-8b5b3015cc11,Windows Update Client LOLBIN,2020-10-17,2023-11-11,high
ca83e9f3-657a-45d0-88d6-c1ac280caf53,New Service Uses Double Ampersand in Path,2022-07-05,2023-11-15,high
fe34868f-6e0e-4882-81f6-c43aa8f15b62,Windows Defender Threat Detection Disabled,2020-07-28,2023-11-22,high
32d0d3e2-e58d-4d41-926b-18b520b2b32d,Credential Dumping Tools Accessing LSASS Memory,2017-02-16,2023-11-30,high
a122ac13-daf8-4175-83a2-72c387be339d,Security Event Log Cleared,2021-08-15,2023-12-06,medium
0332a266-b584-47b4-933d-a00b103e1b37,Suspicious Get-WmiObject,2022-01-12,2023-12-11,low
46deb5e1-28c9-4905-b2df-51cdcc9e6073,PowerShell Scripts Run by a Services,2020-10-06,2023-12-11,high
d23f2ba5-9da0-4463-8908-8ee47f614bb9,Powershell File and Directory Discovery,2021-12-15,2023-12-11,low
df5ff0a5-f83f-4a5b-bba1-3e6a3f6f6ea2,Credential Dumping Tools Service Execution,2017-03-05,2023-12-11,critical
602a1f13-c640-4d73-b053-be9a2fa58b77,Svchost DLL Search Order Hijack,2019-10-28,2024-01-10,high
839dd1e8-eda8-4834-8145-01beeee33acd,SAM Dump to AppData,2018-01-27,2024-01-18,high
e32ce4f5-46c6-4c47-ba69-5de3c9193cd7,Possible Process Hollowing Image Loading,2018-01-07,2024-01-22,high
a6d67db4-6220-436d-8afc-f3842fe05d43,Dnscat Execution,2019-10-24,2024-01-25,critical
d7b09985-95a3-44be-8450-b6eadf49833e,Suspicious Non-Browser Network Communication With Reddit API,2023-02-16,2024-02-02,medium
37325383-740a-403d-b1a2-b2b4ab7992e7,CobaltStrike Malleable (OCSP) Profile,2019-11-12,2024-02-15,high
41b42a36-f62c-4c34-bd40-8cb804a34ad8,CobaltStrike Malformed UAs in Malleable Profiles,2021-05-06,2024-02-15,critical
953b895e-5cc9-454b-b183-7f3db555452e,CobaltStrike Malleable Amazon Browsing Traffic Profile,2019-11-12,2024-02-15,high
c9b33401-cc6a-4cf6-83bb-57ddcb2407fc,CobaltStrike Malleable OneDrive Browsing Traffic Profile,2019-11-12,2024-02-15,high
73fcad2e-ff14-4c38-b11d-4172c8ac86c7,Suspicious Rundll32 Script in CommandLine,2021-12-04,2024-02-23,medium
9f06447a-a33a-4cbe-a94f-a3f43184a7a3,Rundll32 JS RunHTMLApplication Pattern,2022-01-14,2024-02-23,high
e06ac91d-b9e6-443d-8e5b-af749e7aa6b6,iOS Implant URL Pattern,2019-08-30,2024-02-26,critical
628d7a0b-7b84-4466-8552-e6138bc03b43,Suspicious Epmap Connection,2022-07-14,2024-03-01,high
9433ff9c-5d3f-4269-99f8-95fc826ea489,CrackMapExec File Creation Patterns,2022-03-12,2024-03-01,high
277dc340-0540-42e7-8efb-5ff460045e07,Service Binary in Uncommon Folder,2022-05-02,2024-03-25,medium
42f0e038-767e-4b85-9d96-2c6335bad0b5,Adwind RAT / JRAT - Registry,2017-11-10,2024-03-26,high
5039f3d2-406a-4c1a-9350-7a5a85dc84c2,Search-ms and WebDAV Suspicious Indicators in URL,2023-08-21,2024-05-10,high
b916cba1-b38a-42da-9223-17114d846fd6,Potential NT API Stub Patching,2023-01-07,2024-05-27,medium
3d968d17-ffa4-4bc0-bfdc-f139de76ce77,Potential Persistence Via COM Hijacking From Suspicious Locations,2022-07-28,2024-07-16,high
1a3d42dd-3763-46b9-8025-b5f17f340dfb,Suspicious Unattend.xml File Access,2021-12-19,2024-07-22,medium
6902955a-01b7-432c-b32a-6f5f81d8f624,Suspicious File Event With Teams Objects,2022-09-16,2024-07-22,high
a0ff33d8-79e4-4cef-b4f3-9dc4133ccd12,Potential Persistence Via COM Search Order Hijacking,2020-04-14,2024-09-02,medium
a33f8808-2812-4373-ae95-8cfb82134978,Windows Defender Exclusion Deleted,2019-10-26,2025-01-30,medium
e17121b4-ef2a-4418-8a59-12fb1631fa9e,Delete Volume Shadow Copies via WMI with PowerShell - PS Script,2021-12-26,2025-05-20,high
