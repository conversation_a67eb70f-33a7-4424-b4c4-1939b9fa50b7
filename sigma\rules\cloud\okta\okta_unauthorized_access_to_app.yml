title: <PERSON><PERSON> Unauthorized Access to App
id: 6cc2b61b-d97e-42ef-a9dd-8aa8dc951657
status: test
description: Detects when unauthorized access to app occurs.
references:
    - https://developer.okta.com/docs/reference/api/system-log/
    - https://developer.okta.com/docs/reference/api/event-types/
author: <PERSON> @austinsonger
date: 2021-09-12
modified: 2022-10-09
tags:
    - attack.impact
logsource:
    product: okta
    service: okta
detection:
    selection:
        displaymessage: User attempted unauthorized access to app
    condition: selection
falsepositives:
    - User might of believe that they had access.
level: medium
