title: Unix Shell Configuration Modification
id: a94cdd87-6c54-4678-a6cc-2814ffe5a13d
related:
    - id: e74e15cc-c4b6-4c80-b7eb-dfe49feb7fe9
      type: obsolete
status: test
description: Detect unix shell configuration modification. Adversaries may establish persistence through executing malicious commands triggered when a new shell is opened.
references:
    - https://objective-see.org/blog/blog_0x68.html
    - https://web.archive.org/web/20221204161143/https://www.glitch-cat.com/p/green-lambert-and-attack
    - https://www.anomali.com/blog/pulling-linux-rabbit-rabbot-malware-out-of-a-hat
author: <PERSON>, IAI
date: 2023-03-06
modified: 2023-03-15
tags:
    - attack.persistence
    - attack.t1546.004
logsource:
    product: linux
    service: auditd
detection:
    selection:
        type: 'PATH'
        name:
            - '/etc/shells'
            - '/etc/profile'
            - '/etc/profile.d/*'
            - '/etc/bash.bashrc'
            - '/etc/bashrc'
            - '/etc/zsh/zprofile'
            - '/etc/zsh/zshrc'
            - '/etc/zsh/zlogin'
            - '/etc/zsh/zlogout'
            - '/etc/csh.cshrc'
            - '/etc/csh.login'
            - '/root/.bashrc'
            - '/root/.bash_profile'
            - '/root/.profile'
            - '/root/.zshrc'
            - '/root/.zprofile'
            - '/home/<USER>/.bashrc'
            - '/home/<USER>/.zshrc'
            - '/home/<USER>/.bash_profile'
            - '/home/<USER>/.zprofile'
            - '/home/<USER>/.profile'
            - '/home/<USER>/.bash_login'
            - '/home/<USER>/.bash_logout'
            - '/home/<USER>/.zlogin'
            - '/home/<USER>/.zlogout'
    condition: selection
falsepositives:
    - Admin or User activity are expected to generate some false positives
level: medium
