title: Azure DNS Zone Modified or Deleted
id: af6925b0-8826-47f1-9324-337507a0babd
status: test
description: Identifies when DNS zone is modified or deleted.
references:
    - https://learn.microsoft.com/en-us/azure/role-based-access-control/resource-provider-operations#microsoftkubernetes
author: <PERSON> @austinsonger
date: 2021-08-08
modified: 2022-08-23
tags:
    - attack.impact
    - attack.t1565.001
logsource:
    product: azure
    service: activitylogs
detection:
    selection:
        operationName|startswith: 'MICROSOFT.NETWORK/DNSZONES'
        operationName|endswith:
            - '/WRITE'
            - '/DELETE'
    condition: selection
falsepositives:
    - DNS zone modified and deleted may be performed by a system administrator. Verify whether the user identity, user agent, and/or hostname should be making changes in your environment.
    - DNS zone modification from unfamiliar users should be investigated. If known behavior is causing false positives, it can be exempted from the rule.
level: medium
