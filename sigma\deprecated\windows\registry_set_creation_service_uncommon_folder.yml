title: Service Binary in Uncommon Folder
id: 277dc340-0540-42e7-8efb-5ff460045e07
status: deprecated
description: Detect the creation of a service with a service binary located in a uncommon directory
references:
    - https://github.com/redcanaryco/atomic-red-team/blob/f339e7da7d05f6057fdfcdd3742bfcf365fee2a9/atomics/T1562.001/T1562.001.md
author: <PERSON><PERSON><PERSON> (Nextron Systems)
date: 2022/05/02
modified: 2024/03/25
tags:
    - attack.defense_evasion
    - attack.t1112
logsource:
    category: registry_set
    product: windows
detection:
    selection_1:
        TargetObject|startswith: 'HKLM\System\CurrentControlSet\Services\'
        TargetObject|endswith: '\Start'
        Image|contains:
            - '\AppData\Local\'
            - '\AppData\Roaming\'
        Details:
            - 'DWORD (0x00000000)'  # boot
            - 'DWORD (0x00000001)'  # System
            - 'DWORD (0x00000002)'  # Automatic
            # 3 - Manual , 4 - Disabled
    selection_2:
        TargetObject|startswith: 'HKLM\System\CurrentControlSet\Services\'
        TargetObject|endswith: '\ImagePath'
        Details|contains:
            - '\AppData\Local\'
            - '\AppData\Roaming\'
    filter:
        - Image|contains:
              - '\AppData\Roaming\Zoom'
              - '\AppData\Local\Zoom'
        - Details|contains:
              - '\AppData\Roaming\Zoom'
              - '\AppData\Local\Zoom'
    condition: 1 of selection_* and not filter
falsepositives:
    - Unknown
level: medium
