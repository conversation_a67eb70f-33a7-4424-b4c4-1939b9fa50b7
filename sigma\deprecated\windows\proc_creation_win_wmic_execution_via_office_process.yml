title: WMI Execution Via Office Process
id: 518643ba-7d9c-4fa5-9f37-baed36059f6a
related:
    - id: e1693bc8-7168-4eab-8718-cdcaa68a1738
      type: derived
    - id: 438025f9-5856-4663-83f7-52f878a70a50
      type: similar
status: deprecated
description: Initial execution of malicious document calls wmic to execute the file with regsvr32
references:
    - https://thedfirreport.com/2021/03/29/sodinokibi-aka-revil-ransomware/
    - https://github.com/vadim-hunter/Detection-Ideas-Rules/blob/****************************************/Threat%20Intelligence/The%20DFIR%20Report/20210329_Sodinokibi_(aka_REvil)_Ransomware.yaml
author: V<PERSON><PERSON> (ThreatIntel), Cyb3rEng (Rule)
date: 2021/08/23
modified: 2023/02/04
tags:
    - attack.t1204.002
    - attack.t1047
    - attack.t1218.010
    - attack.execution
    - attack.defense_evasion
logsource:
    product: windows
    category: process_creation
detection:
    selection_img:
        - Image|endswith: '\wbem\WMIC.exe'
        - OriginalFileName: 'wmic.exe'
    selection_parent:
        ParentImage|endswith:
            - '\winword.exe'
            - '\excel.exe'
            - '\powerpnt.exe'
    condition: all of selection_*
falsepositives:
    - Unknown
level: medium
