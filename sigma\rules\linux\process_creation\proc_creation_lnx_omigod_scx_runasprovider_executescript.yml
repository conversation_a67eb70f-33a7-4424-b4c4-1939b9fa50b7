title: OMIGOD SCX RunAsProvider ExecuteScript
id: 6eea1bf6-f8d2-488a-a742-e6ef6c1b67db
status: test
description: |
    Rule to detect the use of the SCX RunAsProvider ExecuteScript to execute any UNIX/Linux script using the /bin/sh shell.
    Script being executed gets created as a temp file in /tmp folder with a scx* prefix.
    Then it is invoked from the following directory /etc/opt/microsoft/scx/conf/tmpdir/.
    The file in that directory has the same prefix scx*. SCXcore, started as the Microsoft Operations Manager UNIX/Linux Agent, is now used in a host of products including
    Microsoft Operations Manager, Microsoft Azure, and Microsoft Operations Management Suite.
references:
    - https://www.wiz.io/blog/omigod-critical-vulnerabilities-in-omi-azure
    - https://github.com/Azure/Azure-Sentinel/pull/3059
author: <PERSON> (Cyb3rWard0g), <PERSON><PERSON> (Open Threat Research), MSTIC
date: 2021-10-15
modified: 2022-10-05
tags:
    - attack.privilege-escalation
    - attack.initial-access
    - attack.execution
    - attack.t1068
    - attack.t1190
    - attack.t1203
logsource:
    product: linux
    category: process_creation
detection:
    selection:
        User: root
        LogonId: 0
        CurrentDirectory: '/var/opt/microsoft/scx/tmp'
        CommandLine|contains: '/etc/opt/microsoft/scx/conf/tmpdir/scx'
    condition: selection
falsepositives:
    - Legitimate use of SCX RunAsProvider ExecuteScript.
level: high
