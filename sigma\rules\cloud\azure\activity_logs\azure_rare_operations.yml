title: Rare Subscription-level Operations In Azure
id: c1182e02-49a3-481c-b3de-0fadc4091488
status: test
description: Identifies IPs from which users grant access to other users on azure resources and alerts when a previously unseen source IP address is used.
references:
    - https://github.com/Azure/Azure-Sentinel/blob/e534407884b1ec5371efc9f76ead282176c9e8bb/Detections/AzureActivity/RareOperations.yaml
author: sawwinnnaung
date: 2020-05-07
modified: 2023-10-11
tags:
    - attack.t1003
    - attack.credential-access
logsource:
    product: azure
    service: activitylogs
detection:
    keywords:
        - Microsoft.DocumentDB/databaseAccounts/listKeys/action
        - Microsoft.Maps/accounts/listKeys/action
        - Microsoft.Media/mediaservices/listKeys/action
        - Microsoft.CognitiveServices/accounts/listKeys/action
        - Microsoft.Storage/storageAccounts/listKeys/action
        - Microsoft.Compute/snapshots/write
        - Microsoft.Network/networkSecurityGroups/write
    condition: keywords
falsepositives:
    - Valid change
level: medium
