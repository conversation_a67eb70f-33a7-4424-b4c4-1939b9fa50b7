title: App Granted Microsoft Permissions
id: c1d147ae-a951-48e5-8b41-dcd0170c7213
status: test
description: Detects when an application is granted delegated or app role permissions for Microsoft Graph, Exchange, Sharepoint, or Azure AD
references:
    - https://learn.microsoft.com/en-us/entra/architecture/security-operations-applications#application-granted-highly-privileged-permissions
author: <PERSON> '@baileybercik', <PERSON> '@markmorow'
date: 2022-07-10
tags:
    - attack.credential-access
    - attack.t1528
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message:
            - Add delegated permission grant
            - Add app role assignment to service principal
    condition: selection
falsepositives:
    - When the permission is legitimately needed for the app
level: high
