title: Lateral Movement Indicator ConDrv
id: 29d31aee-30f4-4006-85a9-a4a02d65306c
status: deprecated #Too many FP
description: This event was observed on the target host during lateral movement. The process name within the event contains the process spawned post compromise. Account Name within the event contains the compromised user account name. This event should to be correlated with 4624 and 4688 for further intrusion context.
author: <PERSON><PERSON><PERSON>
date: 2021/04/27
modified: 2022/05/14
references:
    - https://jpcertcc.github.io/ToolAnalysisResultSheet/details/wmiexec-vbs.htm
    - https://www.fireeye.com/blog/threat-research/2017/08/monitoring-windows-console-activity-part-one.html
tags:
    - attack.lateral_movement
    - attack.execution
    - attack.t1021
    - attack.t1059
logsource:
    product: windows
    service: security
detection:
    selection:
          EventID: 4674
          ObjectServer: 'Security'
          ObjectType: 'File'
          ObjectName: '\Device\ConDrv'
    condition: selection
falsepositives:
    - Legal admin action
level: low
