title: App Permissions Granted For Other APIs
id: ba2a7c80-027b-460f-92e2-57d113897dbc
status: deprecated
description: Detects when app permissions (app roles) for other APIs are granted
references:
    - https://docs.microsoft.com/en-us/azure/active-directory/fundamentals/security-operations-applications#application-granted-highly-privileged-permissions
author: <PERSON> '@baileybercik', <PERSON> '@markmorow'
date: 2022/07/28
modified: 2023/03/29
tags:
    - attack.privilege_escalation
logsource:
    product: azure
    service: auditlogs
detection:
    selection:
        properties.message: Add app role assignment to service principal
    condition: selection
falsepositives:
    - When the permission is legitimately needed for the app
level: medium
