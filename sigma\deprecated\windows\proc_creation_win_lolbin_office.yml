title: Suspicious File Download Using Office Application
id: 0c79148b-118e-472b-bdb7-9b57b444cc19
status: test
description: Detects the usage of one of three Microsoft office applications (Word, Excel, PowerPoint) to download arbitrary files
references:
    - https://lolbas-project.github.io/lolbas/OtherMSBinaries/Powerpnt/
    - https://lolbas-project.github.io/lolbas/OtherMSBinaries/Excel/
    - https://lolbas-project.github.io/lolbas/OtherMSBinaries/Winword/
    - https://medium.com/@reegun/unsanitized-file-validation-leads-to-malicious-payload-download-via-office-binaries-202d02db7191
author: <PERSON><PERSON>, oscd.community
date: 2019/10/26
modified: 2023/02/04
tags:
    - attack.command_and_control
    - attack.t1105
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        Image|endswith:
            - '\powerpnt.exe'
            - '\winword.exe'
            - '\excel.exe'
        CommandLine|contains: 'http'
    condition: selection
falsepositives:
    - Unknown
level: high
