title: Too Many Global Admins
id: 7bbc309f-e2b1-4eb1-8369-131a367d67d3
status: test
description: Identifies an event where there are there are too many accounts assigned the Global Administrator role.
references:
    - https://learn.microsoft.com/en-us/entra/id-governance/privileged-identity-management/pim-how-to-configure-security-alerts#there-are-too-many-global-administrators
author: <PERSON> '@markmorow', <PERSON>, '@gleeiamglo'
date: 2023-09-14
tags:
    - attack.t1078
    - attack.persistence
    - attack.privilege-escalation
logsource:
    product: azure
    service: pim
detection:
    selection:
        riskEventType: 'tooManyGlobalAdminsAssignedToTenantAlertIncident'
    condition: selection
falsepositives:
    - Investigate if threshold setting in PIM is too low.
level: high
