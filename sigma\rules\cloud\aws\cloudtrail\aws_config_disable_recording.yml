title: AWS Config Disabling Channel/Recorder
id: 07330162-dba1-4746-8121-a9647d49d297
status: test
description: Detects AWS Config Service disabling
references:
    - https://docs.aws.amazon.com/config/latest/developerguide/cloudtrail-log-files-for-aws-config.html
author: vitaliy0x1
date: 2020-01-21
modified: 2022-10-09
tags:
    - attack.defense-evasion
    - attack.t1562.001
logsource:
    product: aws
    service: cloudtrail
detection:
    selection:
        eventSource: 'config.amazonaws.com'
        eventName:
            - 'DeleteDeliveryChannel'
            - 'StopConfigurationRecorder'
    condition: selection
falsepositives:
    - Valid change in AWS Config Service
level: high
