title: Renamed PsExec
id: a7a7e0e5-1d57-49df-9c58-9fe5bc0346a2
status: deprecated
description: Detects the execution of a renamed PsExec often used by attackers or malware
references:
    - https://www.trendmicro.com/vinfo/hk-en/security/news/cybercrime-and-digital-threats/megacortex-ransomware-spotted-attacking-enterprise-networks
author: <PERSON><PERSON><PERSON> (Nextron Systems)
date: 2019/05/21
modified: 2023/03/04
tags:
    - car.2013-05-009
    - attack.defense_evasion
    - attack.t1036.003
logsource:
    product: windows
    category: process_creation
detection:
    selection:
        Description: 'Execute processes remotely'
        Product: 'Sysinternals PsExec'
    filter:
        Image|endswith:
            - '\PsExec.exe'
            - '\PsExec64.exe'
    condition: selection and not filter
falsepositives:
    - Software that illegaly integrates PsExec in a renamed form
    - Administrators that have renamed PsExec and no one knows why
level: high
